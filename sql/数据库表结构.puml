@startuml ER图

' 设置样式
!define table(x) class x << (T,#FFAAAA) >>
!define primary_key(x) <b><u>x</u></b>
!define foreign_key(x) <i>x</i>
!define column(x) <color:#666666>x</color>
hide methods
hide stereotypes
skinparam linetype ortho
skinparam class {
    BackgroundColor White
    BorderColor Black
    ArrowColor Black
}

' 定义表
table(portal_info) {
    primary_key(id): bigint
    column(portal_name): VARCHAR(255)
    column(portal_code): VARCHAR(255)
    column(portal_title): VARCHAR(255)
    column(portal_logo): TEXT
    column(instance_type): INTEGER
    column(instance_id): bigint
    column(default_app_id): VARCHAR(255)
    column(status): INTEGER
    column(flag_del): INTEGER
    column(remark): VARCHAR(1000)
    column(create_by): bigint
    column(create_time): VARCHAR(30)
    column(update_by): bigint
    column(update_time): VARCHAR(30)
}
note bottom of portal_info : 门户信息

table(dify_instance_info) {
    primary_key(id): bigint
    foreign_key(portal_id): bigint
    column(portal_code): VARCHAR(255)
    column(instance_ip): VARCHAR(255)
    column(instance_port): INTEGER
    column(secret_key): VARCHAR(255)
    column(user_account): VARCHAR(255)
    column(user_password): VARCHAR(255)
    column(flag_del): INTEGER
    column(remark): VARCHAR(1000)
    column(create_by): bigint
    column(create_time): VARCHAR(30)
    column(update_by): bigint
    column(update_time): VARCHAR(30)
}
note bottom of dify_instance_info : Dify实例信息

table(sys_job) {
    primary_key(job_id): bigint
    column(job_name): VARCHAR(255)
    column(job_group): VARCHAR(255)
    column(server_id): VARCHAR(100)
    column(method_url): VARCHAR(1500)
    column(json_param): TEXT
    column(cron_expression): VARCHAR(255)
    column(misfire_policy): VARCHAR(20)
    column(concurrent): VARCHAR(1)
    column(job_status): VARCHAR(1)
    column(created_by): bigint
    column(create_time): VARCHAR(30)
    column(updated_by): bigint
    column(update_time): VARCHAR(30)
    column(remark): VARCHAR(1500)
}
note bottom of sys_job : 定时任务表

table(sys_job_log) {
    primary_key(job_log_id): bigint
    foreign_key(job_id): bigint
    column(job_message): VARCHAR(1500)
    column(status): VARCHAR(1)
    column(exception_info): VARCHAR(6000)
    column(create_time): VARCHAR(30)
    column(trigger_time): bigint
}
note bottom of sys_job_log : 定时任务执行日志表

table(sys_config) {
    primary_key(id): bigint
    column(type_code): VARCHAR(255)
    column(code): VARCHAR(255)
    column(config_value): VARCHAR(512)
    column(flag_encrypt): INTEGER
    column(flag_del): INTEGER
    column(remark): VARCHAR(1000)
    column(create_by): bigint
    column(create_time): VARCHAR(30)
    column(update_by): bigint
    column(update_time): VARCHAR(30)
}
note bottom of sys_config : 系统配置

table(gateway_route) {
    primary_key(id): bigint
    column(route_id): VARCHAR(255)
    column(uri): VARCHAR(255)
    column(predicates): VARCHAR(255)
    column(filters): VARCHAR(255)
    column(order_num): INTEGER
    column(enabled): BOOLEAN
    column(create_time): TIMESTAMP
    column(update_time): TIMESTAMP
    column(create_by): VARCHAR(64)
    column(update_by): VARCHAR(64)
    column(remark): VARCHAR(255)
    column(flag_del): BOOLEAN
}
note bottom of gateway_route : 网关路由配置信息

table(portal_user) {
    primary_key(id): bigint
    column(user_name): VARCHAR(64)
    column(user_pwd): VARCHAR(255)
    column(login_date): TIMESTAMP
    column(create_time): TIMESTAMP
    column(update_time): TIMESTAMP
    column(create_by): VARCHAR(64)
    column(update_by): VARCHAR(64)
    column(flag_del): BOOLEAN
}
note bottom of portal_user : 门户用户信息

table(portal_user_relation) {
    primary_key(id): bigint
    foreign_key(portal_id): bigint
    foreign_key(user_id): bigint
    column(create_time): TIMESTAMP
    column(update_time): TIMESTAMP
    column(create_by): VARCHAR(64)
    column(update_by): VARCHAR(64)
    column(flag_del): BOOLEAN
}
note bottom of portal_user_relation : 门户和用户关系信息

' 定义关系
portal_info "1" -- "1" dify_instance_info : 关联
sys_job "1" -- "0..*" sys_job_log : 记录
portal_info "1" -- "0..*" portal_user_relation : 包含
portal_user "1" -- "0..*" portal_user_relation : 属于

@enduml
