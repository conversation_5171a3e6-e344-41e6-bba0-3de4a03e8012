-- 门户表
DROP TABLE portal_info;
CREATE TABLE portal_info (
                             id int8 NOT NULL, -- ID
                             portal_name varchar(255) NOT NULL, -- 门户名称
                             portal_code varchar(255) NOT NULL, -- 门户编码/URL前缀/实例前缀
                             portal_title varchar(255) NOT NULL, -- 门户标题
                             portal_logo text NOT NULL, -- 门户logo，base64
                             instance_type int4 NULL, -- 实例类型**1-Dify**
                             instance_id int8 NULL, -- 实例ID
                             status int4 NULL, -- 门户状态**1-初始化中 2-初始化失败 3-运行中 4-已停止 5-启动中 6-异常 7-删除中 8-删除失败**
                             status_desc varchar(255) NULL, -- 门户状态描述
                             flag_del int4 DEFAULT 0 NOT NULL, -- 删除标识**0-正常 1-删除**;默认为0
                             remark varchar(1000) NULL, -- 备注
                             create_time timestamp NULL, -- 创建时间
                             update_time timestamp NULL, -- 更新时间
                             create_by varchar(64) NULL, -- 创建人
                             update_by varchar(64) NULL, -- 更新人
                             CONSTRAINT portal_info_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.portal_info IS '门户信息';

-- Column comments

COMMENT ON COLUMN public.portal_info.id IS 'ID';
COMMENT ON COLUMN public.portal_info.portal_name IS '门户名称';
COMMENT ON COLUMN public.portal_info.portal_code IS '门户编码/URL前缀/实例前缀';
COMMENT ON COLUMN public.portal_info.portal_title IS '门户标题';
COMMENT ON COLUMN public.portal_info.portal_logo IS '门户logo，base64';
COMMENT ON COLUMN public.portal_info.instance_type IS '实例类型**1-Dify**';
COMMENT ON COLUMN public.portal_info.instance_id IS '实例ID';
COMMENT ON COLUMN public.portal_info.status IS '门户状态**1-初始化中 2-初始化失败 3-运行中 4-已停止 5-启动中 6-异常 7-删除中 8-删除失败**';
COMMENT ON COLUMN public.portal_info.status_desc IS '门户状态描述';
COMMENT ON COLUMN public.portal_info.flag_del IS '删除标识**0-正常 1-删除**;默认为0';
COMMENT ON COLUMN public.portal_info.remark IS '备注';
COMMENT ON COLUMN public.portal_info.create_time IS '创建时间';
COMMENT ON COLUMN public.portal_info.update_time IS '更新时间';
COMMENT ON COLUMN public.portal_info.create_by IS '创建人';
COMMENT ON COLUMN public.portal_info.update_by IS '更新人';

-- Dify实例表
DROP TABLE dify_instance_info;
CREATE TABLE dify_instance_info (
                                    id int8 NOT NULL, -- ID
                                    portal_id int8 NOT NULL, -- 门户ID
                                    portal_code varchar(255) NOT NULL, -- 门户编码/URL前缀/实例前缀
                                    instance_ip varchar(255) NOT NULL, -- 实例访问IP
                                    instance_port int4 NOT NULL, -- 实例访问端口
                                    secret_key varchar(255) NOT NULL, -- 实例初始化秘钥
                                    user_account varchar(255) NULL, -- 实例管理员账号
                                    user_password varchar(255) NULL, -- 实例管理员密码
                                    flag_del int4 NOT NULL, -- 删除标识**0-正常 1-删除**;默认为0
                                    remark varchar(1000) NULL, -- 备注
                                    create_time timestamp NULL, -- 创建时间
                                    update_time timestamp NULL, -- 更新时间
                                    create_by varchar(64) NULL, -- 创建人
                                    update_by varchar(64) NULL, -- 更新人
                                    CONSTRAINT dify_instance_info_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.dify_instance_info IS 'Dify实例信息';

-- Column comments

COMMENT ON COLUMN public.dify_instance_info.id IS 'ID';
COMMENT ON COLUMN public.dify_instance_info.portal_id IS '门户ID';
COMMENT ON COLUMN public.dify_instance_info.portal_code IS '门户编码/URL前缀/实例前缀';
COMMENT ON COLUMN public.dify_instance_info.instance_ip IS '实例访问IP';
COMMENT ON COLUMN public.dify_instance_info.instance_port IS '实例访问端口';
COMMENT ON COLUMN public.dify_instance_info.secret_key IS '实例初始化秘钥';
COMMENT ON COLUMN public.dify_instance_info.user_account IS '实例管理员账号';
COMMENT ON COLUMN public.dify_instance_info.user_password IS '实例管理员密码';
COMMENT ON COLUMN public.dify_instance_info.flag_del IS '删除标识**0-正常 1-删除**;默认为0';
COMMENT ON COLUMN public.dify_instance_info.remark IS '备注';
COMMENT ON COLUMN public.dify_instance_info.create_time IS '创建时间';
COMMENT ON COLUMN public.dify_instance_info.update_time IS '更新时间';
COMMENT ON COLUMN public.dify_instance_info.create_by IS '创建人';
COMMENT ON COLUMN public.dify_instance_info.update_by IS '更新人';


-- 创建网关路由表
DROP TABLE gateway_route;
CREATE TABLE gateway_route (
                               id int8 NOT NULL, -- 物理主键
                               route_id varchar(255) NOT NULL, -- 路由ID，唯一标识一个路由配置
                               uri varchar(255) NOT NULL, -- 目标URI，表示请求将被转发到的目标地址
                               predicates varchar(255) NOT NULL, -- 断言条件，定义路由匹配的条件（如路径、请求头等）
                               filters varchar(255) DEFAULT NULL::character varying NULL, -- 过滤器，定义在请求被转发前应用的过滤器（如认证、限流等）
                               order_num int4 NOT NULL, -- 路由顺序，用于确定多个路由匹配时的优先级
                               enabled bool NOT NULL, -- 启用标志，表示该路由配置是否生效
                               create_time timestamp NULL, -- 创建时间
                               update_time timestamp NULL, -- 修改时间
                               create_by varchar(255) DEFAULT NULL::character varying NULL, -- 创建者
                               update_by varchar(255) DEFAULT NULL::character varying NULL, -- 修改者
                               remark varchar(255) NULL, -- 备注
                               flag_del int4 DEFAULT 0 NULL, -- 删除标记，默认为未删除（FALSE）
                               CONSTRAINT gateway_route_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.gateway_route IS '存储网关路由配置信息，包括目标URI、匹配条件和过滤器等';

-- Column comments

COMMENT ON COLUMN public.gateway_route.id IS '物理主键';
COMMENT ON COLUMN public.gateway_route.route_id IS '路由ID，唯一标识一个路由配置';
COMMENT ON COLUMN public.gateway_route.uri IS '目标URI，表示请求将被转发到的目标地址';
COMMENT ON COLUMN public.gateway_route.predicates IS '断言条件，定义路由匹配的条件（如路径、请求头等）';
COMMENT ON COLUMN public.gateway_route.filters IS '过滤器，定义在请求被转发前应用的过滤器（如认证、限流等）';
COMMENT ON COLUMN public.gateway_route.order_num IS '路由顺序，用于确定多个路由匹配时的优先级';
COMMENT ON COLUMN public.gateway_route.enabled IS '启用标志，表示该路由配置是否生效';
COMMENT ON COLUMN public.gateway_route.create_time IS '创建时间';
COMMENT ON COLUMN public.gateway_route.update_time IS '修改时间';
COMMENT ON COLUMN public.gateway_route.create_by IS '创建者';
COMMENT ON COLUMN public.gateway_route.update_by IS '修改者';
COMMENT ON COLUMN public.gateway_route.remark IS '备注';
COMMENT ON COLUMN public.gateway_route.flag_del IS '删除标记，默认为未删除（FALSE）';


-- 用户表
DROP TABLE portal_user;
CREATE TABLE portal_user (
                             id int8 NOT NULL, -- 物理主键
                             user_name varchar(64) NOT NULL, -- 用户名称
                             user_pwd varchar(255) NOT NULL, -- 用户密码
                             login_date timestamp NULL, -- 最后登录时间
                             remark varchar(256) NULL,
                             create_time timestamp NULL, -- 创建时间
                             update_time timestamp NULL, -- 修改时间
                             create_by varchar(64) NULL, -- 创建者
                             update_by varchar(64) NULL, -- 修改者
                             source_type int4 DEFAULT 0 NULL, -- 用户来源**1-系统用户 2-LDAP用户**
                             alias_name varchar(64) NULL, --用户别名
                             flag_del int4 DEFAULT 0 NULL -- 删除标记，默认为未删除（FALSE）
);
COMMENT ON TABLE public.portal_user IS '存储门户用户信息';

-- Column comments

COMMENT ON COLUMN public.portal_user.id IS '物理主键';
COMMENT ON COLUMN public.portal_user.user_name IS '用户名称';
COMMENT ON COLUMN public.portal_user.user_pwd IS '用户密码';
COMMENT ON COLUMN public.portal_user.login_date IS '最后登录时间';
COMMENT ON COLUMN public.portal_user.create_time IS '创建时间';
COMMENT ON COLUMN public.portal_user.update_time IS '修改时间';
COMMENT ON COLUMN public.portal_user.create_by IS '创建者';
COMMENT ON COLUMN public.portal_user.update_by IS '修改者';
COMMENT ON COLUMN public.portal_user.source_type IS '用户来源**1-系统用户 2-LDAP用户**';
COMMENT ON COLUMN public.portal_user.alias_name IS '用户别名';
COMMENT ON COLUMN public.portal_user.flag_del IS '删除标记，默认为未删除（FALSE）';


-- 用户-门户关系表
DROP TABLE portal_user_relation;
CREATE TABLE portal_user_relation (
                                      id int8 NOT NULL, -- 物理主键
                                      portal_id int8, -- 门户ID
                                      user_id int8 NOT NULL, -- 用户ID
                                      user_type varchar(255) , -- 用户类型：manager/normal
                                      remark varchar(255) NULL,
                                      create_time timestamp NULL, -- 创建时间
                                      update_time timestamp NULL, -- 修改时间
                                      create_by varchar(255) NULL, -- 创建者
                                      update_by varchar(255) NULL, -- 修改者
                                      flag_del int4 DEFAULT 0 NULL -- 删除标记，默认为未删除（FALSE）
);
COMMENT ON TABLE public.portal_user_relation IS '存储门户和用户之间的关系信息';

-- Column comments

COMMENT ON COLUMN public.portal_user_relation.id IS '物理主键';
COMMENT ON COLUMN public.portal_user_relation.portal_id IS '门户ID';
COMMENT ON COLUMN public.portal_user_relation.user_id IS '用户ID';
COMMENT ON COLUMN public.portal_user_relation.user_type IS '用户类型：manager/normal';
COMMENT ON COLUMN public.portal_user_relation.create_time IS '创建时间';
COMMENT ON COLUMN public.portal_user_relation.update_time IS '修改时间';
COMMENT ON COLUMN public.portal_user_relation.create_by IS '创建者';
COMMENT ON COLUMN public.portal_user_relation.update_by IS '修改者';
COMMENT ON COLUMN public.portal_user_relation.flag_del IS '删除标记，默认为未删除（FALSE）';


-- 系统表
DROP TABLE sys_config;
CREATE TABLE sys_config (
                            id int8 NOT NULL, -- ID
                            type_code varchar(255) NOT NULL, -- 配置项类型
                            code varchar(255) NOT NULL, -- 配置项编码
                            config_value varchar(512) NOT NULL, -- 配置值
                            flag_encrypt int4 NOT NULL, -- 是否加密**0-不加密 1-加密**
                            flag_del int4 NOT NULL, -- 删除标识**0-正常 1-删除**;默认为0
                            remark varchar(1000) NULL, -- 备注
                            create_by int8 NULL, -- 创建人
                            create_time varchar(30) NULL, -- 创建时间
                            update_by int8 NULL, -- 更新人
                            update_time varchar(30) NULL, -- 更新时间
                            CONSTRAINT sys_config_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_config IS '系统配置';

-- Column comments

COMMENT ON COLUMN public.sys_config.id IS 'ID';
COMMENT ON COLUMN public.sys_config.type_code IS '配置项类型';
COMMENT ON COLUMN public.sys_config.code IS '配置项编码';
COMMENT ON COLUMN public.sys_config.config_value IS '配置值';
COMMENT ON COLUMN public.sys_config.flag_encrypt IS '是否加密**0-不加密 1-加密**';
COMMENT ON COLUMN public.sys_config.flag_del IS '删除标识**0-正常 1-删除**;默认为0';
COMMENT ON COLUMN public.sys_config.remark IS '备注';
COMMENT ON COLUMN public.sys_config.create_by IS '创建人';
COMMENT ON COLUMN public.sys_config.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_config.update_by IS '更新人';
COMMENT ON COLUMN public.sys_config.update_time IS '更新时间';


DROP TABLE sys_job;
CREATE TABLE sys_job (
                         job_id int8 NOT NULL, -- 任务号
                         job_name varchar(255) NOT NULL, -- 任务名称
                         job_group varchar(255) NOT NULL, -- 任务组名
                         server_id varchar(100) NOT NULL, -- 服务模块
                         method_url varchar(1500) NULL, -- 调用接口
                         json_param text NOT NULL, -- json格式参数
                         cron_expression varchar(255) NULL, -- CRON执行表达式
                         misfire_policy varchar(20) DEFAULT '3'::character varying NULL, -- 计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）
                         concurrent varchar(1) DEFAULT '1'::character varying NULL, -- 是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）
                         job_status varchar(1) DEFAULT '0'::character varying NULL, -- 状态（0正常 1暂停）
                         created_by int8 NULL, -- 创建人
                         create_time varchar(30) NULL, -- 创建时间
                         updated_by int8 NULL, -- 更新人
                         update_time varchar(30) NULL, -- 更新时间
                         remark varchar(1500) NULL, -- 备注
                         CONSTRAINT sys_job_pkey PRIMARY KEY (job_id)
);
COMMENT ON TABLE public.sys_job IS '定时任务表';

-- Column comments

COMMENT ON COLUMN public.sys_job.job_id IS '任务号';
COMMENT ON COLUMN public.sys_job.job_name IS '任务名称';
COMMENT ON COLUMN public.sys_job.job_group IS '任务组名';
COMMENT ON COLUMN public.sys_job.server_id IS '服务模块';
COMMENT ON COLUMN public.sys_job.method_url IS '调用接口';
COMMENT ON COLUMN public.sys_job.json_param IS 'json格式参数';
COMMENT ON COLUMN public.sys_job.cron_expression IS 'CRON执行表达式';
COMMENT ON COLUMN public.sys_job.misfire_policy IS '计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）';
COMMENT ON COLUMN public.sys_job.concurrent IS '是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）';
COMMENT ON COLUMN public.sys_job.job_status IS '状态（0正常 1暂停）';
COMMENT ON COLUMN public.sys_job.created_by IS '创建人';
COMMENT ON COLUMN public.sys_job.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_job.updated_by IS '更新人';
COMMENT ON COLUMN public.sys_job.update_time IS '更新时间';
COMMENT ON COLUMN public.sys_job.remark IS '备注';


DROP TABLE sys_job_log;
CREATE TABLE sys_job_log (
                             job_log_id int8 NOT NULL, -- 任务日志ID
                             job_id int8 NOT NULL, -- 任务ID
                             job_message varchar(1500) NULL, -- 日志信息
                             status varchar(1) NOT NULL, -- 执行状态（0失败 1正常）
                             exception_info varchar(6000) NULL, -- 异常信息
                             create_time varchar(30) NOT NULL, -- 创建时间
                             trigger_time int8 NULL, -- 触发时间
                             CONSTRAINT sys_job_log_pkey PRIMARY KEY (job_log_id)
);
COMMENT ON TABLE public.sys_job_log IS '定时任务执行日志表';

-- Column comments

COMMENT ON COLUMN public.sys_job_log.job_log_id IS '任务日志ID';
COMMENT ON COLUMN public.sys_job_log.job_id IS '任务ID';
COMMENT ON COLUMN public.sys_job_log.job_message IS '日志信息';
COMMENT ON COLUMN public.sys_job_log.status IS '执行状态（0失败 1正常）';
COMMENT ON COLUMN public.sys_job_log.exception_info IS '异常信息';
COMMENT ON COLUMN public.sys_job_log.create_time IS '创建时间';
COMMENT ON COLUMN public.sys_job_log.trigger_time IS '触发时间';