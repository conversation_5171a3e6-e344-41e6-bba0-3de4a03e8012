@startuml Dify ER图

' 设置样式
!define table(x) class x << (T,#FFAAAA) >>
!define primary_key(x) <b><u>x</u></b>
!define foreign_key(x) <i>x</i>
!define column(x) <color:#666666>x</color>
hide methods
hide stereotypes
skinparam linetype ortho
skinparam class {
    BackgroundColor White
    BorderColor Black
    ArrowColor Black
}

' 定义表
table(accounts) {
    primary_key(id): uuid
    column(name): varchar(255)
    column(email): varchar(255)
    column(password): varchar(255)
    column(password_salt): varchar(255)
    column(avatar): varchar(255)
    column(interface_language): varchar(255)
    column(interface_theme): varchar(255)
    column(timezone): varchar(255)
    column(last_login_at): timestamp
    column(last_login_ip): varchar(255)
    column(status): varchar(16)
    column(initialized_at): timestamp
    column(created_at): timestamp
    column(updated_at): timestamp
    column(last_active_at): timestamp
}
note bottom of accounts : 用户账户

table(tenants) {
    primary_key(id): uuid
    column(name): varchar(255)
    column(encrypt_public_key): text
    column(plan): varchar(255)
    column(status): varchar(255)
    column(created_at): timestamp
    column(updated_at): timestamp
    column(custom_config): text
}
note bottom of tenants : 租户

table(tenant_account_joins) {
    primary_key(id): uuid
    foreign_key(tenant_id): uuid
    foreign_key(account_id): uuid
    column(role): varchar(16)
    column(invited_by): uuid
    column(created_at): timestamp
    column(updated_at): timestamp
    column(current): boolean
}
note bottom of tenant_account_joins : 租户和账户关联

table(apps) {
    primary_key(id): uuid
    foreign_key(tenant_id): uuid
    column(name): varchar(255)
    column(mode): varchar(255)
    column(icon): varchar(255)
    column(icon_background): varchar(255)
    foreign_key(app_model_config_id): uuid
    column(status): varchar(255)
    column(enable_site): boolean
    column(enable_api): boolean
    column(api_rpm): integer
    column(api_rph): integer
    column(is_demo): boolean
    column(is_public): boolean
    column(created_at): timestamp
    column(updated_at): timestamp
    column(is_universal): boolean
    foreign_key(workflow_id): uuid
    column(description): text
    column(tracing): text
    column(max_active_requests): integer
    column(icon_type): varchar(255)
    foreign_key(created_by): uuid
    foreign_key(updated_by): uuid
    column(use_icon_as_answer_icon): boolean
}
note bottom of apps : 应用

table(app_model_configs) {
    primary_key(id): uuid
    foreign_key(app_id): uuid
    column(provider): varchar(255)
    column(model_id): varchar(255)
    column(configs): json
    column(created_at): timestamp
    column(updated_at): timestamp
    column(opening_statement): text
    column(suggested_questions): text
    column(suggested_questions_after_answer): text
    column(more_like_this): text
    column(model): text
    column(user_input_form): text
    column(pre_prompt): text
    column(agent_mode): text
    column(speech_to_text): text
    column(sensitive_word_avoidance): text
    column(retriever_resource): text
    column(dataset_query_variable): varchar(255)
    column(prompt_type): varchar(255)
    column(chat_prompt_config): text
    column(completion_prompt_config): text
    column(dataset_configs): text
    column(external_data_tools): text
    column(file_upload): text
    column(text_to_speech): text
    foreign_key(created_by): uuid
    foreign_key(updated_by): uuid
}
note bottom of app_model_configs : 应用模型配置

table(conversations) {
    primary_key(id): uuid
    foreign_key(app_id): uuid
    foreign_key(app_model_config_id): uuid
    column(model_provider): varchar(255)
    column(override_model_configs): text
    column(model_id): varchar(255)
    column(mode): varchar(255)
    column(name): varchar(255)
    column(summary): text
    column(inputs): json
    column(introduction): text
    column(system_instruction): text
    column(system_instruction_tokens): integer
    column(status): varchar(255)
    column(from_source): varchar(255)
    foreign_key(from_end_user_id): uuid
    foreign_key(from_account_id): uuid
    column(read_at): timestamp
    foreign_key(read_account_id): uuid
    column(created_at): timestamp
    column(updated_at): timestamp
    column(is_deleted): boolean
    column(invoke_from): varchar(255)
    column(dialogue_count): integer
}
note bottom of conversations : 对话

table(messages) {
    primary_key(id): uuid
    foreign_key(app_id): uuid
    column(model_provider): varchar(255)
    column(model_id): varchar(255)
    column(override_model_configs): text
    foreign_key(conversation_id): uuid
    column(inputs): json
    column(query): text
    column(message): json
    column(message_tokens): integer
    column(message_unit_price): numeric(10,4)
    column(answer): text
    column(answer_tokens): integer
    column(answer_unit_price): numeric(10,4)
    column(provider_response_latency): float
    column(total_price): numeric(10,7)
    column(currency): varchar(255)
    column(from_source): varchar(255)
    foreign_key(from_end_user_id): uuid
    foreign_key(from_account_id): uuid
    column(created_at): timestamp
    column(updated_at): timestamp
    column(agent_based): boolean
    column(message_price_unit): numeric(10,7)
    column(answer_price_unit): numeric(10,7)
    foreign_key(workflow_run_id): uuid
    column(status): varchar(255)
    column(error): text
    column(message_metadata): text
    column(invoke_from): varchar(255)
    foreign_key(parent_message_id): uuid
}
note bottom of messages : 消息

table(datasets) {
    primary_key(id): uuid
    foreign_key(tenant_id): uuid
    column(name): varchar(255)
    column(description): text
    column(provider): varchar(255)
    column(permission): varchar(255)
    column(data_source_type): varchar(255)
    column(indexing_technique): varchar(255)
    column(index_struct): text
    foreign_key(created_by): uuid
    column(created_at): timestamp
    foreign_key(updated_by): uuid
    column(updated_at): timestamp
    column(embedding_model): varchar(255)
    column(embedding_model_provider): varchar(255)
    foreign_key(collection_binding_id): uuid
    column(retrieval_model): jsonb
    column(built_in_field_enabled): boolean
}
note bottom of datasets : 数据集

table(documents) {
    primary_key(id): uuid
    foreign_key(tenant_id): uuid
    foreign_key(dataset_id): uuid
    column(position): integer
    column(data_source_type): varchar(255)
    column(data_source_info): text
    foreign_key(dataset_process_rule_id): uuid
    column(batch): varchar(255)
    column(name): varchar(255)
    column(created_from): varchar(255)
    foreign_key(created_by): uuid
    foreign_key(created_api_request_id): uuid
    column(created_at): timestamp
    column(processing_started_at): timestamp
    column(file_id): text
    column(word_count): integer
    column(parsing_completed_at): timestamp
    column(cleaning_completed_at): timestamp
    column(splitting_completed_at): timestamp
    column(tokens): integer
    column(indexing_latency): float
    column(completed_at): timestamp
    column(is_paused): boolean
    foreign_key(paused_by): uuid
    column(paused_at): timestamp
    column(error): text
    column(stopped_at): timestamp
    column(indexing_status): varchar(255)
    column(enabled): boolean
    column(disabled_at): timestamp
    foreign_key(disabled_by): uuid
    column(archived): boolean
    column(archived_reason): varchar(255)
    foreign_key(archived_by): uuid
    column(archived_at): timestamp
    column(updated_at): timestamp
    column(doc_type): varchar(40)
    column(doc_metadata): jsonb
    column(doc_form): varchar(255)
    column(doc_language): varchar(255)
}
note bottom of documents : 文档

table(document_segments) {
    primary_key(id): uuid
    foreign_key(tenant_id): uuid
    foreign_key(dataset_id): uuid
    foreign_key(document_id): uuid
    column(position): integer
    column(content): text
    column(word_count): integer
    column(tokens): integer
    column(keywords): json
    column(index_node_id): varchar(255)
    column(index_node_hash): varchar(255)
    column(hit_count): integer
    column(enabled): boolean
    column(disabled_at): timestamp
    foreign_key(disabled_by): uuid
    column(status): varchar(255)
    foreign_key(created_by): uuid
    column(created_at): timestamp
    column(indexing_at): timestamp
    column(completed_at): timestamp
    column(error): text
    column(stopped_at): timestamp
    column(answer): text
    foreign_key(updated_by): uuid
    column(updated_at): timestamp
}
note bottom of document_segments : 文档片段

table(providers) {
    primary_key(id): uuid
    foreign_key(tenant_id): uuid
    column(provider_name): varchar(255)
    column(provider_type): varchar(40)
    column(encrypted_config): text
    column(is_valid): boolean
    column(last_used): timestamp
    column(quota_type): varchar(40)
    column(quota_limit): bigint
    column(quota_used): bigint
    column(created_at): timestamp
    column(updated_at): timestamp
}
note bottom of providers : 提供商

table(provider_models) {
    primary_key(id): uuid
    foreign_key(tenant_id): uuid
    column(provider_name): varchar(255)
    column(model_name): varchar(255)
    column(model_type): varchar(40)
    column(encrypted_config): text
    column(is_valid): boolean
    column(created_at): timestamp
    column(updated_at): timestamp
}
note bottom of provider_models : 提供商模型

table(workflows) {
    primary_key(id): uuid
    foreign_key(tenant_id): uuid
    foreign_key(app_id): uuid
    column(type): varchar(255)
    column(version): varchar(255)
    column(graph): text
    column(features): text
    foreign_key(created_by): uuid
    column(created_at): timestamp
    foreign_key(updated_by): uuid
    column(updated_at): timestamp
    column(environment_variables): text
    column(conversation_variables): text
    column(marked_name): varchar
    column(marked_comment): varchar
}
note bottom of workflows : 工作流

' 定义关系
tenants "1" -- "0..*" tenant_account_joins : 包含
accounts "1" -- "0..*" tenant_account_joins : 属于
tenants "1" -- "0..*" apps : 拥有
apps "1" -- "1" app_model_configs : 配置
apps "1" -- "0..*" conversations : 包含
conversations "1" -- "0..*" messages : 包含
tenants "1" -- "0..*" datasets : 拥有
datasets "1" -- "0..*" documents : 包含
documents "1" -- "0..*" document_segments : 包含
tenants "1" -- "0..*" providers : 配置
tenants "1" -- "0..*" provider_models : 配置
apps "1" -- "0..1" workflows : 使用

@enduml
