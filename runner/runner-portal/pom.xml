<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.sansec.ai</groupId>
		<artifactId>ai-unified-portal</artifactId>
		<version>1.0-SNAPSHOT</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>

    <artifactId>runner-portal</artifactId>
	<name>ai-unified-portal</name>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <sonar.projectKey>runner-portal</sonar.projectKey>
        <sonar.projectName>runner-portal</sonar.projectName>
    </properties>

	<dependencies>
<!--		<dependency>-->
<!--			<groupId>com.sansec.ai</groupId>-->
<!--			<artifactId>common-task</artifactId>-->
<!--		</dependency>-->
		<dependency>
			<groupId>com.sansec.ai</groupId>
			<artifactId>portal-management-service</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sansec.ai</groupId>
			<artifactId>user-manager-service</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sansec.ai</groupId>
			<artifactId>app-management-service</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
    </dependencies>
    <build>
        <finalName>${project.name}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <finalName>${project.name}</finalName>
                </configuration>
                <executions>  <!--执行器 mvn assembly:assembly-->
                    <execution>
                        <id>release</id><!--名字任意 -->
                        <phase>package</phase><!-- 绑定到package生命周期阶段上 -->
                        <goals>
                            <goal>single</goal><!-- 只运行一次 -->
                        </goals>
                        <configuration>
                            <descriptors> <!--描述文件路径-->
                                <descriptor>../../assembly/assembly.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>