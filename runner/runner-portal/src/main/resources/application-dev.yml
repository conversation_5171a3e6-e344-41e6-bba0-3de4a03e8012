spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *********************************************************************
    username: zwKh91pHiXgXLiOL86rmEQ==
    password: a7TWhSiUaCCJamLfHT13KQ==
  #ldap
  ldap:
    enable: true
    urls: ldap://10.0.8.18
    base: ou=三未信安科技股份有限公司,dc=sansec,dc=cn
    username: bpHiDZ4bONo/KZ+PdT+++ukF6xedyo7mIiJXUWzmWqzXQw7RMgf9ft1FRkTq/zYV
    password: ymVVhoIS0hhQ+lGFO46+Xw==
    #用作本系统中用户登录ID的LDAP属性
    login_attr: sAMAccountName
    #组织结构和门户的关联关系
    org-path: classpath:config/organization.json
    #根组织绑定的门户
    root-portal-code: sansec

mybatis:
  databaseType: postgresql

ai:
  #网关（特指Nginx，非Spring Cloud Gateway网关）负责代理统一门户的请求到后端服务、Dify的请求到Dify实例
  gateway-url: https://HOST_REAL_IP:9443
  portal:
    #最大门户数量
    max-count: 8
  dify:
    #Dify实例部署根路径，该路径下包含：模板目录、所有Dify实例目录
    root_dir: /opt/sansec/dify
    #宿主机IP
    instance_ip: HOST_REAL_IP