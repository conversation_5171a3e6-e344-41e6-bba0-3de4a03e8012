package com.sansec.ai;

import com.sansec.component.algorithm.utill.ProviderUtil;
import com.sansec.jce.provider.SwxaProvider;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.AnnotationBeanNameGenerator;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.security.Security;

@EnableAsync
@EnableScheduling
@SpringBootApplication
@ComponentScan(basePackages = {"com.sansec.ai.**", "com.sansec.plat.**"}, nameGenerator = PortalApplication.SpringBeanNameGenerator.class)
@MapperScan(basePackages = {"com.sansec.ai.**.mapper", "com.sansec.plat.**.mapper"}, sqlSessionTemplateRef = "sqlSessionTemplate")
@EnableTransactionManagement
public class PortalApplication {

	public static class SpringBeanNameGenerator extends AnnotationBeanNameGenerator {
		@Override
		public String generateBeanName(BeanDefinition definition, BeanDefinitionRegistry registry) {
			String shortClassName = super.generateBeanName(definition, registry);

			// 如果存在相同名称的bean，则将要注册的bean的名称设置为全路径名
			if (registry.containsBeanDefinition(shortClassName)) {
				return definition.getBeanClassName();
			}

			return shortClassName;
		}
	}

	public static void main(String[] args) {
		if (!ProviderUtil.isJceEnabled()) {
			Security.addProvider(new SwxaProvider(null, null));
			System.setProperty("SANSEC.SSL", "TRUE");
		}
		new SpringApplication(PortalApplication.class).run(args);
	}
}
