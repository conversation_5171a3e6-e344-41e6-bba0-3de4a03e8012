package com.sansec.ai;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.HexUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sansec.ai.auth.ldap.config.LdapConfiguration;
import com.sansec.ai.auth.ldap.entity.LdapUser;
import com.sansec.ai.auth.ldap.service.ILdapService;
import com.sansec.common.utils.SM2Util;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.ldap.core.LdapTemplate;

import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/2/28 11:13
 */
@Slf4j
@SpringBootTest
public class CommonServiceTest {

	@Value("${secplat.base.packagepri}")
	private String privateKey;

	@Value("${secplat.base.packagepub}")
	private String publicKey;

	@Resource
	private ILdapService ldapService;

	@Test
	void decrypt() {
		//前端给的私钥
		String privateKeyF = Base64.getEncoder().encodeToString(HexUtil.decodeHex("22914c8dbf904c3b69682fcc8d06b044895e5c85a3eed29f72d0c7dcabac7b73"));
		System.out.println(privateKeyF);
		//前端给的公钥
		String publicKeyF = Base64.getEncoder().encodeToString(HexUtil.decodeHex("77d987bb38e0ba805a3c89d8c85531a5e953bf5e3e6d118bdda176e22370930741dad7d38ecf225b548dde59f8b8f2a761951240904129dda4b921f0f561bbbb"));
		System.out.println(publicKeyF);
		//前端SM2+base64后的字符串
		String password = "xMzfVp4Rrht/iKGzyXWizl07s5j0RgfspDi+SQ+cw0BNdGuyRaHqyI0jE4+6h6CR0D/GdBqu7IQIMJ9xUrxxDKuAihoSVqzdzSmIqOyYscPYrrBm/t2bfBNRXb9P3Dn6kNf6X9mWTisv";
		String decrypt = SM2Util.decrypt(privateKeyF, password);
		System.out.println(new String(Base64.getDecoder().decode(decrypt)));
	}

	@Resource
	private LdapTemplate ldapTemplate;
	@Resource
	private LdapConfiguration ldapConfiguration;

	@Test
	void ldap_login() {
//		boolean success = ldapService.authenticate("<EMAIL>", "Five1991");
//		boolean success = ldapService.authenticate("zhaiwuhao", "Five1991");
//		System.out.println("login result = " + success);
		LdapUser user = ldapService.getLdapUser("zhujianming");
		System.out.println("user: " + JSON.toJSONString(user));
		for (String orgName : user.getOrgNameList()) {
			//直到找到对应的组织
			String portalCode = ldapConfiguration.getPortalCode(orgName);
			if(portalCode != null){
				System.out.println("portalCode: " + portalCode);
				break;
			}
		}
//		System.out.println(user.getOrgNameList());
//		List<LdapUser> list = ldapService.searchLdapUser("zhaiwuhao");
//		System.out.println("user list: " + JSON.toJSONString(list));
//		JSONObject org = new JSONObject();
//		org.put("orgName", "三未信安科技股份有限公司");
//		org.put("subList", getSubList(null));
//		System.out.println(org.toJSONString());
	}

	JSONArray getSubList(String parentOU) {
		List<String> subList = ldapTemplate.list(parentOU == null ? "" : parentOU);
		if (CollUtil.isEmpty(subList)) {
			return null;
		}
		JSONArray array = new JSONArray();
		for (String orgName : subList) {
			if(orgName.startsWith("CN=")){
				continue;
			}
			JSONObject org = new JSONObject();
			org.put("orgName", orgName.replace("OU=", ""));
			array.add(org);
			//子组织
			JSONArray subArray = getSubList(parentOU == null ? orgName : orgName + "," + parentOU);
			if (subArray == null) {
				continue;
			}
			org.put("subList", subArray);
		}
		return array;
	}

}
