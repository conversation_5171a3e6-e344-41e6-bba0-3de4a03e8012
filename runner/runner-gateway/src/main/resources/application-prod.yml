server:
  port: 18083

spring:
  datasource:
    url: jdbc:postgresql://${LOCAL_HOST:127.0.0.1}:5432/sec_plat_web?currentSchema=public&stringtype=unspecified&autoReconnect=true&useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&ssl=false
    username: ${MYSQL_USERNAME}
    password: ${MYSQL_PASSWORD}
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false

sansec:
  ai:
    default-route: portal_service
    cookie-name: dify<PERSON><PERSON><PERSON>