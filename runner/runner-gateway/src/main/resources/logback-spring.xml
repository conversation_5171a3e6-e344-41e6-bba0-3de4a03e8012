<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定义日志文件的路径和名称 -->
    <property name="LOG_FILE" value="${LOG_FILE:-logs/ai-gateway.log}" />

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 滚动日志文件 -->
    <appender name="ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>

        <!-- 滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 按日期和文件大小滚动 -->
            <fileNamePattern>logs/ai-gateway-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- 单个日志文件的最大大小 -->
            <maxFileSize>10MB</maxFileSize>
            <!-- 保留的日志文件最大数量 -->
            <maxHistory>30</maxHistory>
            <!-- 所有日志文件的最大总大小 -->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 设置日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="ROLLING_FILE" />
    </root>
</configuration>