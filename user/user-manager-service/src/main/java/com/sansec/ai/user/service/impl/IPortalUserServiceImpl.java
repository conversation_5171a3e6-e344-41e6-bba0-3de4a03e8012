package com.sansec.ai.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.benmanes.caffeine.cache.Cache;
import com.sansec.ai.common.base.entity.ImageCode;
import com.sansec.ai.common.base.enums.EnumDelFlag;
import com.sansec.ai.common.base.enums.TokenTypeEnum;
import com.sansec.ai.common.base.utils.ImageCodeGenerator;
import com.sansec.ai.common.base.utils.TokenUtil;
import com.sansec.ai.portal.entity.vo.DifyInstanceInfoVo;
import com.sansec.ai.portal.service.IPortalService;
import com.sansec.ai.user.constant.EnableEnum;
import com.sansec.ai.user.constant.PortalLoginResEnum;
import com.sansec.ai.user.constant.UserTypeEnum;
import com.sansec.ai.user.entity.po.PortalUser;
import com.sansec.ai.user.entity.po.PortalUserRelation;
import com.sansec.ai.user.entity.request.CaptchaGetDTO;
import com.sansec.ai.user.entity.request.PortalUserAddDTO;
import com.sansec.ai.user.entity.request.PortalUserLoginDTO;
import com.sansec.ai.user.entity.request.PortalUserModifyDTO;
import com.sansec.ai.user.entity.vo.CaptchaGetVO;
import com.sansec.ai.user.entity.vo.PortalUserVO;
import com.sansec.ai.user.entity.vo.UserStatusVO;
import com.sansec.ai.user.mapper.PortalUserMapper;
import com.sansec.ai.user.mapper.PortalUserRelationMapper;
import com.sansec.ai.user.service.IPortalUserService;
import com.sansec.ai.user.service.PortalUserLockService;
import com.sansec.ai.user.service.PortalUserService;
import com.sansec.common.exception.BusinessException;
import com.sansec.plat.base.utils.Pbkdf2Tools;
import jakarta.annotation.Resource;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.imageio.ImageIO;
import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.sansec.ai.user.constant.ErrorCodeConst.*;

/**
 * @Author: WanJun
 * @Date: 2025/5/17 15:15
 * @Description:
 */
@Service
@Slf4j
public class IPortalUserServiceImpl extends ServiceImpl<PortalUserMapper, PortalUser> implements IPortalUserService {

	@Resource
	private PortalUserRelationMapper portalUserRelationMapper;

	@Resource
	Cache<String, ImageCode> portalVerifyCodeCache;

	@Resource
	private Cache<String, Object> portalUserCache;

	@Resource
	private PortalUserLockService portalUserLockService;

	@Resource
	private PortalUserMapper portalUserMapper;
	@Resource
	private IPortalService portalService;
	@Resource
	private PortalUserService portalUserService;

	@Value("${ai.portal.cookie-name}")
	private String cookieName;

	/**
	 * 获取门户用户信息
	 *
	 * @param userId 用户id
	 * @return 用户信息
	 */
	@Override
	public PortalUserVO get(Long userId) {
		PortalUser portalUser = this.getById(userId);
		if (portalUser == null) {
			throw new BusinessException(USER_NOT_EXISTS);
		}

		PortalUserVO portalUserVO = new PortalUserVO();
		portalUserVO.setId(portalUser.getId());
		portalUserVO.setUserName(portalUser.getUserName());
		portalUserVO.setLoginDate(portalUser.getLoginDate());
		portalUserVO.setAliasName(portalUser.getAliasName());
		PortalUserRelation portalUserRelation = portalUserRelationMapper.selectOne(Wrappers.lambdaQuery(PortalUserRelation.class).eq(PortalUserRelation::getUserId, userId));
		if (portalUserRelation != null) {
			portalUserVO.setPortalId(portalUserRelation.getPortalId());
		}
		return portalUserVO;
	}

	/**
	 * 获取门户用户信息
	 *
	 * @param userName
	 * @param portalId
	 * @return 用户信息
	 */
	@Override
	public PortalUserVO getByUserName(String userName, Long portalId) {
		PortalUser user = this.getOne(Wrappers.lambdaQuery(PortalUser.class)
				.eq(PortalUser::getFlagDel, EnumDelFlag.NORMAL.getKey())
				.eq(PortalUser::getUserName, userName), false);
		if (user == null) {
			return null;
		}
		PortalUserVO userVo = new PortalUserVO();
		userVo.setId(user.getId());
		userVo.setUserName(user.getUserName());
		userVo.setLoginDate(user.getLoginDate());
		// 用户来源**1-系统用户 2-LDAP用户**
		userVo.setSourceType(user.getSourceType());
		// 用户别名
		userVo.setAliasName(user.getAliasName());

		PortalUserRelation relation = portalUserRelationMapper.selectOne(Wrappers.lambdaQuery(PortalUserRelation.class).eq(PortalUserRelation::getUserId, user.getId()));
		if (relation != null) {
			userVo.setPortalId(relation.getPortalId());
		}
		if (portalId != null) {
			if (relation == null) {
				log.error("未找到用户匹配的门户[userName={}, portalId={}]", userName, portalId);
				return null;
			}
			if (portalId.longValue() != relation.getPortalId()) {
				log.error("用户门户不匹配[userName={}, portalId={}, relation={}]", userName, portalId, JSON.toJSONString(relation));
				return null;
			}
		}
		return userVo;
	}

	/**
	 * 生成图片验证码
	 *
	 * @return 返回图片验证码id
	 */
	@Override
	public CaptchaGetVO generateCaptcha() {
		String key = IdUtil.getSnowflakeNextIdStr();
		ImageCode imageCode = ImageCodeGenerator.generate();
		portalVerifyCodeCache.put(key, imageCode);
		try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
			ImageIO.write(imageCode.getImage(), "png", baos); // 可以改为需要的图片格式，如"jpg"
			byte[] imageBytes = baos.toByteArray();
			CaptchaGetVO result = CaptchaGetVO.builder().captchaId(key)
					.captchaBase64(Base64.encodeBase64String(imageBytes)).build();
			result.setCaptchaBase64("data:image/png;base64," + result.getCaptchaBase64());
			return result;
		} catch (Exception e) {
			throw new BusinessException(USER_CAPTCHA_CODE_WRITE_TO_RESPONSE_ERROR);
		}
	}

	/**
	 * 根据验证码id获取验证码
	 *
	 * @param captchaGetDTO 验证码id
	 * @return 图片验证码写入响应
	 */
	@Override
	public ImageCode getCaptcha(CaptchaGetDTO captchaGetDTO) {
		// 1.参数校验
		if (captchaGetDTO == null || StringUtils.isEmpty(captchaGetDTO.getCaptchaId())) {
			throw new BusinessException(USER_CAPTCHA_ID_NOT_BE_BLANK);
		}
		// 2.从缓存中获取验证码
		ImageCode imageCode = portalVerifyCodeCache.getIfPresent(captchaGetDTO.getCaptchaId());
		if (imageCode == null) {
			throw new BusinessException(USER_CAPTCHA_CODE_EXPIRED);
		}
		return imageCode;
	}

	@Override
	public String login(PortalUserLoginDTO dto) {
		// 1、参数校验
		if (portalUserLockService.isImageCodeDisplay(dto.getUserName()) || StringUtils.isNotBlank(dto.getCaptchaId())) {
			ImageCode imageCode1 = portalVerifyCodeCache.getIfPresent(dto.getCaptchaId());
			if (imageCode1 == null) {
				throw new BusinessException(USER_CAPTCHA_CODE_EXPIRED);
			}

			if (!imageCode1.getCode().equalsIgnoreCase(dto.getCaptcha())) {
				portalVerifyCodeCache.invalidate(dto.getCaptchaId());
				throw new BusinessException(USER_CAPTCHA_CODE_VERIFY_ERROR);
			}
			portalVerifyCodeCache.invalidate(dto.getCaptchaId());
		}
		// NOTE 校验请求中是否携带了门户唯一标志
		// 从cookie中获取门户唯一标志
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		if (ArrayUtils.isEmpty(request.getCookies())) {
			throw new BusinessException(LOGIN_PORTAL_CODE_NOT_EXIST);
		}
		Optional<Cookie> cookie = Arrays.stream(request.getCookies()).filter(item -> cookieName.equals(item.getName())).findFirst();
		if (cookie.isEmpty()) {
			throw new BusinessException(LOGIN_PORTAL_CODE_NOT_EXIST);
		}


		// 2、检查用户是否存在
		PortalUser portalUser = portalUserMapper.selectOne(Wrappers.lambdaQuery(PortalUser.class).eq(PortalUser::getUserName, dto.getUserName()));
		if (portalUser == null) {
			throw new BusinessException(USER_NOT_EXISTS);
		}
		portalUserLockService.getFlagLock(portalUser.getUserName());
		// 3、用户名和密码是否正确
		boolean authCodeCorrect = Pbkdf2Tools.validateAuch(dto.getUserPwd(), portalUser.getUserPwd());
		if (!authCodeCorrect) {
			portalUserLockService.lockUser(portalUser.getUserName());
			throw new BusinessException(USER_AUTH_CODE_NOT_CORRECT_ERROR);
		}

		List<PortalUserRelation> userRelations = portalUserRelationMapper.selectList(Wrappers.lambdaQuery(PortalUserRelation.class).eq(PortalUserRelation::getUserId, portalUser.getId()));
		if (CollUtil.isEmpty(userRelations)) {
			throw new BusinessException(USER_NOT_BIND_PORTAL);
		}
		DifyInstanceInfoVo portalInfo = portalService.getDifyInstanceInfoByPortalId(userRelations.get(0).getPortalId());
		if (portalInfo == null) {
			throw new BusinessException(USER_NOT_BIND_PORTAL);
		}
		// NOTE 校验用户是否有权登录该门户
		if (!portalInfo.getPortalCode().equals(cookie.get().getValue())) {
			throw new BusinessException(LOGIN_PORTAL_USER_NOT_MATCH);
		}

		// 4、生成token并放入缓存
		String token = TokenUtil.createToken(portalUser.getId(), String.valueOf(UserTypeEnum.PORTAL_USER.getCode()), portalInfo.getPortalCode());
		portalUserCache.put(token, portalUser.getId());
		portalUserLockService.release(portalUser.getUserName());
		this.update(Wrappers.lambdaUpdate(PortalUser.class).eq(PortalUser::getId, portalUser.getId())
				.set(PortalUser::getLoginDate, new Date()));
		// 5、返回信息
		return token;
	}

	/**
	 * 三方用户登录
	 *
	 * @param userId
	 * @param userType
	 * @param portalCode
	 * @return
	 */
	@Override
	public String thirdLogin(Long userId, UserTypeEnum userType, String portalCode) {
		// 生成token并放入缓存
		String token = TokenUtil.createToken(userId, String.valueOf(userType.getCode()), portalCode);
		portalUserCache.put(token, userId);
		this.update(Wrappers.lambdaUpdate(PortalUser.class)
				.eq(PortalUser::getId, userId)
				.set(PortalUser::getLoginDate, new Date()));
		return token;
	}

	@Override
	public void logout() {
		String token = TokenUtil.getTokenFromHeader(TokenTypeEnum.AI_PORTAL_TOKEN);
		if (StringUtils.isNotBlank(token)) {
			portalUserCache.invalidate(token);
		}
	}

	@Override
	public PortalLoginResEnum checkToken(String token, String requestUri) {
		//校验token
		log.info("requestUti:{},token:{}", requestUri, token);
		if (StringUtils.isBlank(token)) {
			log.info("requestUti:{},token is null", requestUri);
			return PortalLoginResEnum.TOKEN_ERROR;
		}
		Long userId = TokenUtil.getUserId(TokenTypeEnum.AI_PORTAL_TOKEN);
		if (userId == null) {
			log.info("token parsing error");
			return PortalLoginResEnum.TOKEN_ERROR;
		}

		//检查token是否失效
		Long userIdInCache = (Long) portalUserCache.getIfPresent(token);
		if (userIdInCache == null) {
			log.info("token expiration");
			return PortalLoginResEnum.TOKEN_ERROR;
		}

		//检查是否为用户伪造的token
		if (userId.longValue() != userIdInCache) {
			log.info("token invalid[userId={}, cacheUserId={}]", userId, userIdInCache);
			return PortalLoginResEnum.TOKEN_ERROR;
		}

		//重新更新内存，可以刷新用户token的过期时间
		portalUserCache.put(token, userIdInCache);
		return PortalLoginResEnum.SUCCESS;
	}

	@Override
	public UserStatusVO getUserStatus(String userName) {
		UserStatusVO userStatusVO = new UserStatusVO();
		userStatusVO.setCodeDisplay(EnableEnum.DISABLE.getCode());
		if (StringUtils.isNotBlank(userName)) {
			boolean imageCodeDisplay = portalUserLockService.isImageCodeDisplay(userName);
			if (imageCodeDisplay) {
				userStatusVO.setCodeDisplay(EnableEnum.ENABLE.getCode());
			}
		}
		return userStatusVO;
	}

	@Override
	public void modify(PortalUserModifyDTO dto) {
		// 1、参数校验

		// 2、查询用户是否存在
		PortalUser portalUser = this.getById(dto.getUserId());
		if (portalUser == null) {
			throw new BusinessException(USER_NOT_EXISTS);
		}
		if (StringUtils.isNotBlank(dto.getOldUserPwd())) {
			boolean authCodeCorrect = Pbkdf2Tools.validateAuch(dto.getOldUserPwd(), portalUser.getUserPwd());
			if (!authCodeCorrect) {
				throw new BusinessException(OLD_USER_AUTH_CODE_NOT_CORRECT_ERROR);
			}
			boolean newPwdSameWithOld = Pbkdf2Tools.validateAuch(dto.getUserPwd(), portalUser.getUserPwd());
			if (newPwdSameWithOld) {
				throw new BusinessException(NEW_USER_AUTH_CODE_SAME_WITH_OLD_ERROR);
			}
		}

		// 3、修改用户密码
		String userPwdEnc = Pbkdf2Tools.generateEncryptedStr(dto.getUserPwd());
		portalUser.setUserPwd(userPwdEnc);

		this.update(Wrappers.lambdaUpdate(PortalUser.class).eq(PortalUser::getId, dto.getUserId())
				.set(PortalUser::getUserPwd, userPwdEnc));

	}

	@Override
	public Long add(PortalUserAddDTO dto) {
		return portalUserService.add(dto);
	}
}