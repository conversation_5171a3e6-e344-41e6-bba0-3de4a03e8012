package com.sansec.ai.user.entity.po;

import com.sansec.ai.common.base.entity.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PortalUser extends BaseEntity {
    private Long id;
    private String userName;
    private String userPwd;
    private Date loginDate;
	/**
	 * 用户来源**1-系统用户 2-LDAP用户**
	 */
	private Integer sourceType;
	/**
	 * 用户别名
	 */
	private String aliasName;
}
