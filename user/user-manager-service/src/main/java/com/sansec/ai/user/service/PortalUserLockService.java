package com.sansec.ai.user.service;

public interface PortalUserLockService {

    /**
     * 获取用户是否超过锁定时间
     * @param userName
     * @return
     */
    void getFlagLock(String userName);

    /**
     * 锁定用户
     * @param userName
     */
    void lockUser(String userName);

    /**
     * 释放用户
     * @param userName
     */
    void release(String userName);

	/**
	 * 根据登录次数，判断是否显示验证码
	 * @param userName
	 * @return
	 */
    boolean isImageCodeDisplay(String userName);
}
