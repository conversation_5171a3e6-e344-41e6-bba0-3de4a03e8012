package com.sansec.ai.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ai.user.constant.UserTypeEnum;
import com.sansec.ai.user.entity.request.PortalManagerAddDTO;
import com.sansec.ai.user.entity.request.PortalManagerDeleteDTO;
import com.sansec.ai.user.entity.request.PortalManagerListDTO;
import com.sansec.ai.user.entity.po.PortalUserRelation;
import com.sansec.ai.user.mapper.PortalUserRelationMapper;
import com.sansec.ai.user.service.IPortalManagerService;
import com.sansec.ai.user.entity.vo.PortalManagerVO;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.utils.SM3Util;
import com.sansec.plat.user.entity.SecRole;
import com.sansec.plat.user.entity.SecUser;
import com.sansec.plat.user.entity.SecUserRole;
import com.sansec.plat.user.mapper.SecUserMapper;
import com.sansec.plat.user.service.SecRoleService;
import com.sansec.plat.user.service.SecUserRoleService;
import com.sansec.plat.user.service.SecUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sansec.ai.common.base.constant.ErrorCode.PARAM_VERIFY_FAILED;
import static com.sansec.ai.user.constant.CommonConstants.*;
import static com.sansec.ai.user.constant.ErrorCodeConst.USER_NOT_EXISTS;

/**
 * @Author: WanJun
 * @Date: 2025/5/12 16:48
 * @Description:
 */
@Service
@Slf4j
public class PortalManagerServiceImpl extends ServiceImpl<PortalUserRelationMapper, PortalUserRelation> implements IPortalManagerService {

    @Resource
    private SecUserService secUserService;

    @Resource
    private SecRoleService secRoleService;

    @Resource
    private SecUserRoleService secUserRoleService;

    @Resource
    private SecUserMapper secUserMapper;


    /**
     * 新增门户管理员
     *
     * @param dto 请求信息
     * @return 响应信息
     */
    @Override
    public Long add(PortalManagerAddDTO dto) {
        // 1、参数校验
        log.debug("收到创建管理员的请求，请求信息为：名称-{}，加密密码的长度-{}，门户id-{}", dto.getUserName(), StringUtils.isNotBlank(dto.getUserPwd()) ? dto.getUserPwd().length() : 0, dto.getPortalId());

        // 2、判断是否创建新用户
        if(StringUtils.isEmpty( dto.getUserPwd()) || dto.getUserPwd().length() != 64){
            log.error("管理员密码格式不对，长度应该为64，实际为：", StringUtils.isNotBlank(dto.getUserPwd()) ? dto.getUserPwd().length() : 0);
            throw new BusinessException(PARAM_VERIFY_FAILED);
        }
        String defaultAuthCode = DEFAULT_AUTH_CODE + System.currentTimeMillis();
        String digestWithBase64 = SM3Util.digestWithHex(defaultAuthCode);
        if (dto.getAddNewUser()) {
            SecRole role = secRoleService.getOne(Wrappers.lambdaQuery(SecRole.class).eq(SecRole::getRoleCode, PORTAL_MANAGER_CODE));
            // 2.1 如果创建新用户，调用web平台创建用户
            SecUser secUser = new SecUser();
            secUser.setUserName(dto.getUserName());
            secUser.setUserPwd(digestWithBase64);
            secUser.setRoleIds(String.valueOf(role.getId()));
            secUser.setUserType(NAME_PWD_LOGIN_USER);
            secUser.setTenantCode(com.sansec.ai.common.base.utils.TokenUtil.getTenantCode(dto.getSecToken()));
            secUserService.insert(secUser);
            dto.setUserId(String.valueOf(secUser.getId()));
        }
        if (StringUtils.isEmpty(dto.getUserId())) {
            throw new BusinessException(PARAM_VERIFY_FAILED);
        }
        SecUser user = secUserService.selectById(Long.valueOf(dto.getUserId()));
        if (user == null) {
            throw new BusinessException(USER_NOT_EXISTS);
        }

        // 3、重置口令
        user.setUserPwd(dto.getUserPwd());
        secUserService.update(user);

        // 4、存储关联关系
        this.remove(Wrappers.lambdaQuery(PortalUserRelation.class).eq(PortalUserRelation::getUserId, user.getId()));
        PortalUserRelation portalUserRelation = new PortalUserRelation();
        portalUserRelation.setPortalId(dto.getPortalId());
        portalUserRelation.setUserId(Long.valueOf(dto.getUserId()));
        portalUserRelation.setUserType(dto.getUserType());
        this.save(portalUserRelation);

        // 5、返回
        return user.getId();
    }

    /**
     * 删除门户管理员
     *
     * @param dto 请求信息
     * @return 响应，门户管理员id
     */
    @Override
    @Transactional
    public Long delete(PortalManagerDeleteDTO dto) {
        // 1、参数校验
        log.debug("收到删除管理员的请求，请求信息为：门户id-{}，是否删除门户管理员-{}", dto.getPortalId(), dto.getDeleteUser());
        if (dto.getPortalId() == null || dto.getDeleteUser() == null) {
            throw new BusinessException(PARAM_VERIFY_FAILED);
        }

        // 3、如果需要删除管理员，则删除平台侧的用户
        if (dto.getDeleteUser()) {
            // 4、根据门户id查询关联的管理员
            List<PortalUserRelation> userRelations = this.list(Wrappers.lambdaQuery(PortalUserRelation.class)
                    .eq(PortalUserRelation::getPortalId, dto.getPortalId())
                    .eq(PortalUserRelation::getUserType, UserTypeEnum.PORTAL_MANAGER.type));
            if (CollUtil.isNotEmpty(userRelations)) {
                userRelations.forEach(item ->{
                    secUserMapper.deleteById(item.getUserId());
                    secUserRoleService.deleteByUserId(item.getUserId());
                });
            }
        }

        // 2、删除关联关系
        this.remove(Wrappers.lambdaQuery(PortalUserRelation.class).eq(PortalUserRelation::getPortalId, dto.getPortalId()));

        // 5、 返回
        return dto.getPortalId();
    }

    /**
     * 信息
     * 查询门户管理员列表
     *
     * @param dto
     * @return 门户管理员列表
     */
    @Override
    public List<PortalManagerVO> list(PortalManagerListDTO dto) {
        // 1、调用web平台查看用户名like传值的用户
        QueryWrapper<SecUser> secUserQueryWrapper = new QueryWrapper<>();
        secUserQueryWrapper.lambda().like(StringUtils.isNotBlank(dto.getUserName()), SecUser::getUserName, dto.getUserName());
        List<SecUser> secUsers = secUserService.queryByCondition(secUserQueryWrapper);
        if (CollUtil.isEmpty(secUsers)) {
            return new ArrayList<>();
        }
        Map<Long, String> userNames = secUsers.stream().collect(Collectors.toMap(SecUser::getId, SecUser::getUserName));

        // 2、查询oper用户对应的角色
        String operCode = PORTAL_MANAGER_CODE;
        QueryWrapper<SecUserRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(SecUserRole::getRoleCode, operCode).in(SecUserRole::getUserId, secUsers.stream().map(SecUser::getId).toList());
        List<SecUserRole> userRoleList = secUserRoleService.list(queryWrapper);
        if (CollUtil.isEmpty(userRoleList)) {
            return new ArrayList<>();
        }
        // 2.1 获取oper角色下的用户id，包含了绑定门户的和没有绑定门户的
        ArrayList<Long> userIds = new ArrayList<>(userRoleList.stream().map(SecUserRole::getUserId).toList());
        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        // 3、查询关联关系表，查询绑定的用户
        List<PortalUserRelation> portalUserRelations = this.list(Wrappers.lambdaQuery(PortalUserRelation.class)
                .eq(dto.getPortalId() != null, PortalUserRelation::getPortalId, dto.getPortalId())
                .in(PortalUserRelation::getUserId, userIds));

        if (dto.getBindStatus() != null) {
            if (dto.getBindStatus()) {
                if (CollUtil.isEmpty(portalUserRelations)) {
                    return new ArrayList<>();
                }
                return portalUserRelations.stream().map(portalUserRelation -> {
                    PortalManagerVO portalManagerVO = new PortalManagerVO();
                    portalManagerVO.setId(portalUserRelation.getUserId());
                    portalManagerVO.setUserName(userNames.get(portalUserRelation.getUserId()));
                    portalManagerVO.setPortalId(portalUserRelation.getPortalId());
                    return portalManagerVO;
                }).toList();
            } else {
                userIds.removeAll(portalUserRelations.stream().map(PortalUserRelation::getUserId).toList());
            }
        }
        return userIds.stream().map(userId -> {
            PortalManagerVO portalManagerVO = new PortalManagerVO();
            portalManagerVO.setId(userId);
            portalManagerVO.setUserName(userNames.get(userId));
            return portalManagerVO;
        }).toList();

    }

	/**
	 * 根据门户ID查询门户管理员
	 *
	 * @param portalId 门户ID
	 * @return 门户管理员信息
	 */
	@Override
	public PortalManagerVO getByPortalId(Long portalId) {
		PortalUserRelation relation  = getOne(Wrappers.lambdaQuery(PortalUserRelation.class)
				.eq(PortalUserRelation::getPortalId, portalId)
				.eq(PortalUserRelation::getUserType, UserTypeEnum.PORTAL_MANAGER.type), false);
		if(relation == null){
			return null;
		}
		return get(relation.getUserId());
	}

	/**
     * 根据id查询门户管理员信息
     *
     * @param userId 管理员id
     * @return 门户管理员信息
     */
    @Override
    public PortalManagerVO get(Long userId) {
        // 1、调用web平台查询用户是否存在
        SecUser secUser = secUserService.selectById(userId);

        if (secUser == null) {
            return null;
        }

        PortalManagerVO portalManagerVO = new PortalManagerVO();
        portalManagerVO.setId(secUser.getId());
        portalManagerVO.setUserName(secUser.getUserName());

        // 2、查询用户门户关联信息
	    PortalUserRelation portalUserRelation = this.getOne(Wrappers.lambdaQuery(PortalUserRelation.class).eq(PortalUserRelation::getUserId, secUser.getId()));
	    if (portalUserRelation != null) {
	        portalManagerVO.setPortalId(portalUserRelation.getPortalId());
	    }

	    // 查询用户角色
	    List<SecUserRole> roleList = secUserRoleService.list(new LambdaQueryWrapper<SecUserRole>().eq(SecUserRole::getUserId, userId));
	    if(!CollUtil.isEmpty(roleList)){
		    portalManagerVO.setRoleCode(roleList.get(0).getRoleCode());
	    }

	    // 3、封装响应
        return portalManagerVO;
    }

    /**
     * 校验用户名称是否存在
     *
     * @param userName 用户名称
     * @return true:存在 false:不存在
     */
    @Override
    public Boolean checkManagerExist(String userName) {
        SecUser secUser = secUserService.queryByUsername(userName);
        return secUser != null;
    }
}