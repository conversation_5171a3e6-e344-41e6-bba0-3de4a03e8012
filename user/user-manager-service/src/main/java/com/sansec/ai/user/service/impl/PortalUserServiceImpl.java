package com.sansec.ai.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ai.common.base.enums.TokenTypeEnum;
import com.sansec.ai.common.base.utils.TokenUtil;
import com.sansec.ai.portal.entity.vo.PortalListVo;
import com.sansec.ai.portal.entity.vo.PortalSiteInfoVo;
import com.sansec.ai.portal.service.IPortalService;
import com.sansec.ai.user.constant.UserTypeEnum;
import com.sansec.ai.user.entity.convert.PortalUserConvert;
import com.sansec.ai.user.entity.po.PortalUser;
import com.sansec.ai.user.entity.po.PortalUserRelation;
import com.sansec.ai.user.entity.request.PortalUserAddDTO;
import com.sansec.ai.user.entity.request.PortalUserIdsDTO;
import com.sansec.ai.user.entity.request.PortalUserModifyDTO;
import com.sansec.ai.user.entity.request.PortalUserPageDTO;
import com.sansec.ai.user.entity.vo.PortalManagerVO;
import com.sansec.ai.user.entity.vo.PortalUserPageVO;
import com.sansec.ai.user.entity.vo.PortalUserVO;
import com.sansec.ai.user.mapper.PortalUserMapper;
import com.sansec.ai.user.mapper.PortalUserRelationMapper;
import com.sansec.ai.user.service.IPortalManagerService;
import com.sansec.ai.user.service.PortalUserService;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.plat.base.utils.Pbkdf2Tools;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sansec.ai.user.constant.ErrorCodeConst.*;


/**
 * @Author: WanJun
 * @Date: 2024/10/24 10:25
 * @Description: 用户管理接口实现类
 */
@Service
@Slf4j
public class PortalUserServiceImpl extends ServiceImpl<PortalUserMapper, PortalUser> implements PortalUserService {

	@Resource
	private PortalUserRelationMapper portalUserRelationMapper;

	@Resource
	private IPortalManagerService portalManagerService;

	@Resource
	private PortalUserConvert convert;
	@Resource
	private IPortalService portalService;

	@Override
	public Long add(PortalUserAddDTO dto) {
		// 1、参数校验

		// 2、判断用户名称是否重复
		boolean userNameExist = this.exists(Wrappers.lambdaQuery(PortalUser.class).eq(PortalUser::getUserName, dto.getUserName()));
		if (userNameExist) {
			throw new BusinessException(USER_NAME_ALREADY_EXIST);
		}

		// 3、构造用户密码密文
		String userPwdEnc = Pbkdf2Tools.generateEncryptedStr(dto.getUserPwd());

		// 4、保存用户信息
		PortalUser portalUser = new PortalUser();
		portalUser.setUserName(dto.getUserName());
		portalUser.setUserPwd(userPwdEnc);
		portalUser.setAliasName(dto.getAliasName());
		portalUser.setSourceType(dto.getSourceType() == null ? 1 : dto.getSourceType());
		this.save(portalUser);

		// 5、保存用户-门户关联信息
		if (dto.getPortalId() != null) {
			PortalUserRelation portalUserRelation = new PortalUserRelation();
			portalUserRelation.setPortalId(dto.getPortalId());
			portalUserRelation.setUserId(portalUser.getId());
			portalUserRelation.setUserType(UserTypeEnum.PORTAL_USER.type);
			portalUserRelationMapper.insert(portalUserRelation);
		}


		// 6、返回
		return portalUser.getId();
	}

	@Override
	public void modify(PortalUserModifyDTO dto) {
		// 1、参数校验

		// 2、查询用户是否存在
		PortalUser portalUser = this.getById(dto.getUserId());
		if (portalUser == null) {
			throw new BusinessException(USER_NOT_EXISTS);
		}
		if (org.apache.commons.lang3.StringUtils.isNotBlank(dto.getOldUserPwd())) {
			boolean authCodeCorrect = Pbkdf2Tools.validateAuch(dto.getOldUserPwd(), portalUser.getUserPwd());
			if (!authCodeCorrect) {
				throw new BusinessException(OLD_USER_AUTH_CODE_NOT_CORRECT_ERROR);
			}
			boolean newPwdSameWithOld = Pbkdf2Tools.validateAuch(dto.getUserPwd(), portalUser.getUserPwd());
			if (newPwdSameWithOld) {
				throw new BusinessException(NEW_USER_AUTH_CODE_SAME_WITH_OLD_ERROR);
			}
		}

		// 3、修改用户密码
		String userPwdEnc = Pbkdf2Tools.generateEncryptedStr(dto.getUserPwd());
		portalUser.setUserPwd(userPwdEnc);

		this.update(Wrappers.lambdaUpdate(PortalUser.class).eq(PortalUser::getId, dto.getUserId())
				.set(PortalUser::getUserPwd, userPwdEnc));
	}

	@Override
	public void delete(PortalUserIdsDTO dto) {
		if (dto != null && CollUtil.isNotEmpty(dto.getUserIds())) {
			this.removeBatchByIds(dto.getUserIds());
			portalUserRelationMapper.delete(Wrappers.lambdaQuery(PortalUserRelation.class).in(PortalUserRelation::getUserId, dto.getUserIds()));
		}
	}

	@Override
	public SecPageVO<PortalUserPageVO> page(PortalUserPageDTO dto) {
		LambdaQueryWrapper<PortalUser> wrapper = Wrappers.lambdaQuery(PortalUser.class)
				.like(StringUtils.isNotEmpty(dto.getUserName()), PortalUser::getUserName, dto.getUserName());
		List<PortalUserRelation> bindUsers = portalUserRelationMapper.selectList(Wrappers.lambdaQuery(PortalUserRelation.class)
				.eq(PortalUserRelation::getUserType, UserTypeEnum.PORTAL_USER.type));
		List<Long> allBindPortalUserIds = new ArrayList<>(bindUsers.stream().map(PortalUserRelation::getUserId).toList());

		Map<Long, Long> userPortalMap = bindUsers.stream().collect(Collectors.toMap(PortalUserRelation::getUserId, PortalUserRelation::getPortalId));
		Boolean enableBind;
		// 1、如果查询未绑定门户的用户
		if (dto.getBindStatus() != null && !dto.getBindStatus()) {
			enableBind = false;
			// 1.2 根据名称查询，并排除1.1返回结果
			wrapper.notIn(CollUtil.isNotEmpty(allBindPortalUserIds), PortalUser::getId, allBindPortalUserIds);
		}
		// 2、如果查询绑定门户的用户或者绑定状态未传参
		else {
			Long userId = TokenUtil.getUserId(TokenTypeEnum.SEC_PLAT_TOKEN);
			PortalManagerVO portalManagerVO = portalManagerService.get(userId);
			List<Long> bindPortalUserIds;
			if (portalManagerVO != null && portalManagerVO.getPortalId() != null) {
				enableBind = true;
				bindPortalUserIds = new ArrayList<>(portalUserRelationMapper.selectList(Wrappers.lambdaQuery(PortalUserRelation.class)
								.eq(PortalUserRelation::getPortalId, portalManagerVO.getPortalId())
								.eq(PortalUserRelation::getUserType, UserTypeEnum.PORTAL_USER.type))
						.stream().map(PortalUserRelation::getUserId).toList());
			} else {
				enableBind = false;
				bindPortalUserIds = new ArrayList<>();
			}

			// 0、如果绑定状态没有传
			if (dto.getBindStatus() == null) {
				// 0.1 查询绑定当前门户或者未绑定门户的用户，即过滤绑定的不是当前门户的id即可
				if (CollUtil.isNotEmpty(allBindPortalUserIds)) {
					if (CollUtil.isNotEmpty(bindPortalUserIds)) {
						allBindPortalUserIds.removeAll(bindPortalUserIds);
					}
					wrapper.notIn(CollUtil.isNotEmpty(allBindPortalUserIds), PortalUser::getId, allBindPortalUserIds);
				}
			} else {
				// 如果绑定状态为true 2.2 根据名称查询且id在绑定当前门户的用户
				if (CollUtil.isEmpty(bindPortalUserIds)) {
					return new SecPageVO<>(dto.getPageNum(), dto.getPageSize(), 0, new ArrayList<>());
				}
				wrapper.in(CollUtil.isNotEmpty(bindPortalUserIds), PortalUser::getId, bindPortalUserIds);
			}
		}

		// 3、返回结果
		IPage<PortalUser> page = Page.of(dto.getPageNum(), dto.getPageSize());
		IPage<PortalUser> pagination = this.page(page, wrapper);
		SecPageVO<PortalUserPageVO> pageList = convert.toPagedList(pagination);
		for (PortalUserPageVO vo : pageList.getList()) {
			vo.setBindStatus(false);
			vo.setEnableBind(enableBind);
			Long portalId = userPortalMap.get(vo.getId());
			if (portalId != null) {
				vo.setPortalId(portalId);
				vo.setBindStatus(true);
				PortalSiteInfoVo portal = portalService.getPortalSiteInfo(portalId);
				vo.setPortalName(portal.getPortalName());
			}
		}

		return pageList;
	}

	/**
	 * 查询所有门户用户
	 *
	 * @param dto
	 * @return
	 */
	@Override
	public SecPageVO<PortalUserPageVO> pageAll(PortalUserPageDTO dto) {
		// 分页查询
		IPage<PortalUser> page = new Page<>(dto.getPageNum(), dto.getPageSize());
		IPage<PortalUserPageVO> pagination = baseMapper.getPagedPortalUserList(page, dto);

		//补全参数
		for (PortalUserPageVO vo : pagination.getRecords()) {
			//绑定状态
			vo.setBindStatus(vo.getPortalId() != null);
			//是否可以绑定
			vo.setEnableBind(vo.getPortalId() == null);
			if (vo.getPortalId() != null) {
				//门户名称
				PortalSiteInfoVo portal = portalService.getPortalSiteInfo(vo.getPortalId());
				vo.setPortalName(portal.getPortalName());
			}
		}

		return new SecPageVO<>((int) pagination.getPages(), (int) pagination.getSize(), pagination.getTotal(), pagination.getRecords());
	}

	@Override
	public SecRestResponse<String> bind(PortalUserIdsDTO dto) {
		// 1、参数校验
		if (dto == null || CollUtil.isEmpty(dto.getUserIds())) {
			return SecRestResponse.success();
		}
		// 2、查询管理员及门户id
		if (dto.getPortalId() == null) {
			//NOTE 如果未传门户ID，则默认绑定当前管理员所属的门户
			Long managerId = TokenUtil.getUserId(TokenTypeEnum.SEC_PLAT_TOKEN);
			List<PortalUserRelation> portalUserRelations = portalUserRelationMapper.selectList(Wrappers.lambdaQuery(PortalUserRelation.class).eq(PortalUserRelation::getUserId, managerId));
			if (CollUtil.isEmpty(portalUserRelations) || portalUserRelations.size() > 1) {
				throw new BusinessException(PORTAL_MANAGER_BIND_PORTAL_ERROR);
			}
			PortalUserRelation portalUserRelation = portalUserRelations.get(0);
			dto.setPortalId(portalUserRelation.getPortalId());
		}
		else{
			//NOTE 如果传了门户ID，则判断是否有权绑定
			PortalManagerVO manager = portalManagerService.getByPortalId(dto.getPortalId());
			if(manager != null && manager.getPortalId() != null){
				if(manager.getPortalId().longValue() != dto.getPortalId()){
					throw new BusinessException(PORTAL_MANAGER_BIND_PORTAL_ERROR);
				}
			}

		}
		// 3、判断要绑定的用户没有绑定其他门户
		boolean exists = portalUserRelationMapper.exists(Wrappers.lambdaQuery(PortalUserRelation.class).in(PortalUserRelation::getUserId, dto.getUserIds()));
		if (exists) {
			throw new BusinessException(SOME_USER_ALREADY_BIND_PORTAL);
		}

		// 4、保存绑定信息
		dto.getUserIds().forEach(userId -> {
			PortalUserRelation model = new PortalUserRelation();
			model.setPortalId(dto.getPortalId());
			model.setUserId(userId);
			model.setUserType(UserTypeEnum.PORTAL_USER.type);
			portalUserRelationMapper.insert(model);
		});

		// 5、返回
		return SecRestResponse.success(String.valueOf(dto.getPortalId()));
	}

	@Override
	public SecRestResponse<String> unbind(PortalUserIdsDTO dto) {
		portalUserRelationMapper.delete(Wrappers.lambdaQuery(PortalUserRelation.class).in(PortalUserRelation::getUserId, dto.getUserIds()));
		return SecRestResponse.success();
	}

	@Override
	public PortalUserVO get(Long userId) {
		PortalUser portalUser = this.getById(userId);
		if (portalUser == null) {
			throw new BusinessException(USER_NOT_EXISTS);
		}

		PortalUserVO portalUserVO = new PortalUserVO();
		portalUserVO.setId(portalUser.getId());
		portalUserVO.setUserName(portalUser.getUserName());
		portalUserVO.setLoginDate(portalUser.getLoginDate());
		PortalUserRelation portalUserRelation = portalUserRelationMapper.selectOne(Wrappers.lambdaQuery(PortalUserRelation.class).eq(PortalUserRelation::getUserId, userId));
		if (portalUserRelation != null) {
			portalUserVO.setPortalId(portalUserRelation.getPortalId());
		}
		return portalUserVO;
	}
}