package com.sansec.ai.user.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ai.user.entity.po.GatewayRoute;
import com.sansec.ai.user.entity.request.GatewayAddDTO;
import com.sansec.ai.user.mapper.GatewayRouteMapper;
import com.sansec.ai.user.service.IGatewayService;
import com.sansec.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static com.sansec.ai.user.constant.CommonConstants.*;
import static com.sansec.ai.user.constant.ErrorCodeConst.ROUTE_ID_REPEAT;

/**
 * @Author: WanJun
 * @Date: 2025/5/17 13:38
 * @Description:
 */
@Service
@Slf4j
public class GatewayRouteServiceImpl extends ServiceImpl<GatewayRouteMapper, GatewayRoute> implements IGatewayService {
	/**
	 * 添加网关
	 *
	 * @param dto 请求
	 * @return id
	 */
	@Override
	public Long add(GatewayAddDTO dto) {
		// 1、判断routeId是否重复
		boolean exists = this.exists(Wrappers.lambdaQuery(GatewayRoute.class).eq(GatewayRoute::getRouteId, dto.getRouteId()));
		if (exists) {
			throw new BusinessException(ROUTE_ID_REPEAT);
		}

		// 2、构造实体类
		GatewayRoute route = new GatewayRoute();
		route.setRouteId(dto.getRouteId());
		route.setEnabled(true);
		route.setUri(dto.getUri());
		route.setOrderNum(ROUTE_ORDER);
		// 2.1 请求前缀不为空时构造成断言
		if (StringUtils.isNotBlank(dto.getCookieValue())) {
			route.setPredicates(PREDICATE_TEMPLATE_WITH_COOKIE.replaceAll(PLACE_HOLDER, dto.getCookieValue()));
		} else {
			route.setPredicates(PREDICATE_TEMPLATE);
		}

		// 2.2 cookie值不为空时构造成过滤器
//        if(StringUtils.isNotBlank(dto.getCookieValue())){
//            route.setFilters(FILTER_TEMPLATE.replaceAll(PLACE_HOLDER, dto.getCookieValue()));
//        }

		// 3、 落库
		this.save(route);

		return route.getId();
	}

	/**
	 * 根据门户id删除路由
	 *
	 * @param routeId 路由id，对应门户id或门户名称
	 * @return 路由id
	 */
	@Override
	public Long delete(String routeId) {
		this.remove(Wrappers.lambdaQuery(GatewayRoute.class).eq(GatewayRoute::getRouteId, routeId));
		return Long.valueOf(routeId);
	}
}