package com.sansec.ai.user.constant;

/**
 * 是否启用标识
 */
public enum EnableEnum {

    DISABLE(0, "禁用"),
    ENABLE(1, "启用");

    private Integer code;

    private String msg;

    EnableEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }


    /**
     * 校验存在
     * @param code
     * @return
     */
    public static boolean checkExist(Integer code){
        if(code==null){
            return false;
        }
        for(EnableEnum enableEnum :EnableEnum.values()){
            if(code.equals(enableEnum.code)){
                return true;
            }
        }
        return false;
    }

}
