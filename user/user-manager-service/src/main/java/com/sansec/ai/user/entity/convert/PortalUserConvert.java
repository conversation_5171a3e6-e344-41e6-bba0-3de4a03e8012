package com.sansec.ai.user.entity.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ai.user.entity.po.PortalUser;
import com.sansec.ai.user.entity.vo.PortalUserPageVO;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * 门户用户信息
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
@Mapper(componentModel = "spring")
public interface PortalUserConvert {

	@Mappings({
			@Mapping(source = "size", target = "pageSize"),
			@Mapping(source = "current", target = "pageNum"),
			@Mapping(source = "records", target = "list")
	})
	SecPageVO<PortalUserPageVO> toPagedList(IPage<PortalUser> page);

//	@InheritConfiguration(name = "convertVo")
//	List<PortalListVo> convert(List<PortalInfo> list);
//
//	@Mappings({})
//	PortalListVo convertVo(PortalInfo request);
//
//	@Mappings({})
//	PortalSiteInfoVo toSiteInfo(PortalInfo request);
//
//	@Mappings({})
//	PortalDetailVo toPortalDetail(PortalInfo request);
}