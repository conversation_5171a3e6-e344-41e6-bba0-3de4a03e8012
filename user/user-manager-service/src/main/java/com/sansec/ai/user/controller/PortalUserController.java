package com.sansec.ai.user.controller;


import com.sansec.ai.common.base.constant.RequestAttributeConst;
import com.sansec.ai.user.constant.UserTypeEnum;
import com.sansec.ai.user.entity.request.PortalUserAddDTO;
import com.sansec.ai.user.entity.request.PortalUserIdsDTO;
import com.sansec.ai.user.entity.request.PortalUserModifyDTO;
import com.sansec.ai.user.entity.request.PortalUserPageDTO;
import com.sansec.ai.user.entity.vo.PortalUserPageVO;
import com.sansec.ai.user.entity.vo.PortalUserVO;
import com.sansec.ai.user.service.PortalUserService;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * @Author: WanJun
 * @Date: 2024/10/23 15:11
 * @Description:
 */

/**
 * 门户用户管理
 */
@RestController
@RequestMapping(value = "/manager/user/")
public class PortalUserController {

	@Resource
	private PortalUserService portalUserService;


	/**
	 * 新增用户
	 *
	 * @param dto 用户新增请求
	 * @return 用户id
	 */
	@PostMapping("add")
	public SecRestResponse<String> add(@RequestBody PortalUserAddDTO dto) {
		Long userId = portalUserService.add(dto);
		return SecRestResponse.success(String.valueOf(userId));
	}

	/**
	 * 修改用户
	 *
	 * @param dto 用户修改请求
	 * @return 用户id
	 */
	@PostMapping("modify")
	public SecRestResponse<String> modify(@RequestBody PortalUserModifyDTO dto) {
		portalUserService.modify(dto);
		return SecRestResponse.success(String.valueOf(dto.getUserId()));
	}

	/**
	 * 删除用户
	 *
	 * @param dto 用户删除请求
	 * @return 无
	 */
	@PostMapping("delete")
	public SecRestResponse<String> delete(@RequestBody PortalUserIdsDTO dto) {
		portalUserService.delete(dto);
		return SecRestResponse.success();
	}

	/**
	 * 分页查询用户
	 *
	 * @param dto 用户分页请求
	 * @return 用户分页响应
	 */
	@PostMapping("page")
	public SecRestResponse<SecPageVO<PortalUserPageVO>> page(@RequestBody PortalUserPageDTO dto) {
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (attributes == null) {
			return null;
		}
		UserTypeEnum userType = (UserTypeEnum) attributes.getRequest().getAttribute(RequestAttributeConst.USER_TYPE);
		if (userType == UserTypeEnum.SYSTEM_MANAGER) {
			return SecRestResponse.success(portalUserService.pageAll(dto));
		}
		return SecRestResponse.success(portalUserService.page(dto));
	}

	/**
	 * 绑定用户
	 *
	 * @param dto 用户绑定请求
	 * @return 无
	 */
	@PostMapping("bind")
	public SecRestResponse<String> bind(@RequestBody PortalUserIdsDTO dto) {
		return portalUserService.bind(dto);
	}

	/**
	 * 解绑用户
	 *
	 * @param dto 用户解绑请求
	 * @return 无
	 */
	@PostMapping("unbind")
	public SecRestResponse<String> unbind(@RequestBody PortalUserIdsDTO dto) {
		return portalUserService.unbind(dto);
	}

	/**
	 * 获取用户信息
	 *
	 * @param userId 用户id
	 * @return 用户信息
	 */
	@GetMapping("get")
	public SecRestResponse<PortalUserVO> get(Long userId) {
		return SecRestResponse.success(portalUserService.get(userId));
	}
}