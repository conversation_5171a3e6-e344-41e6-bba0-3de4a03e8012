package com.sansec.ai.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ai.portal.entity.request.PortalListPagedRequest;
import com.sansec.ai.portal.entity.vo.PortalListVo;
import com.sansec.ai.user.entity.po.PortalUser;
import com.sansec.ai.user.entity.request.PortalUserPageDTO;
import com.sansec.ai.user.entity.vo.PortalUserPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: WanJun
 * @Date: 2025/5/12 09:41
 * @Description:
 */
@Mapper
public interface PortalUserMapper extends BaseMapper<PortalUser> {

	/**
	 * 获取门户用户列表
	 *
	 * @param page
	 * @param request
	 * @return
	 */
	IPage<PortalUserPageVO> getPagedPortalUserList(IPage<PortalUser> page, @Param("param") PortalUserPageDTO request);
}