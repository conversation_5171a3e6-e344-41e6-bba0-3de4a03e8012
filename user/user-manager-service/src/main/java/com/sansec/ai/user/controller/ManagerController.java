package com.sansec.ai.user.controller;


import com.sansec.ai.common.base.enums.TokenTypeEnum;
import com.sansec.ai.common.base.utils.TokenUtil;
import com.sansec.ai.portal.entity.vo.PortalSiteInfoVo;
import com.sansec.ai.portal.service.IPortalService;
import com.sansec.ai.user.entity.request.PortalManagerListDTO;
import com.sansec.ai.user.entity.vo.PortalManagerVO;
import com.sansec.ai.user.service.IPortalManagerService;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecRestResponse;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.sansec.ai.user.constant.ErrorCodeConst.USER_NOT_EXISTS;

/**
 * @Author: WanJun
 * @Date: 2024/10/23 15:11
 * @Description:
 */

/**
 * 门户管理员
 */
@RestController
@RequestMapping(value = "/manager/")
public class ManagerController {

	@Resource
	private IPortalManagerService portalManagerService;

	@Resource
	private IPortalService portalService;


	/**
	 * 列表查询
	 *
	 * @param dto 请求信息
	 * @return 门户管理员列表
	 */
	@PostMapping(value = "list")
	public SecRestResponse<List<PortalManagerVO>> list(@RequestBody PortalManagerListDTO dto) {
		return SecRestResponse.success(portalManagerService.list(dto));
	}

	@GetMapping(value = "get")
	public SecRestResponse<PortalManagerVO> get() {
		PortalManagerVO manager = portalManagerService.get(TokenUtil.getUserId(TokenTypeEnum.SEC_PLAT_TOKEN));
		if (manager == null) {
			throw new BusinessException(USER_NOT_EXISTS);
		}
		if (manager.getPortalId() != null) {
			PortalSiteInfoVo portalInfo = portalService.getPortalSiteInfo(manager.getPortalId());
			if (portalInfo != null) {
				manager.setPortalName(portalInfo.getPortalName());
			}
		}
		return SecRestResponse.success(manager);
	}
}