package com.sansec.ai.user.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.sansec.ai.common.base.entity.UserLock;
import com.sansec.ai.user.constant.FLagLockEnum;
import com.sansec.ai.user.service.PortalUserLockService;
import com.sansec.common.exception.BusinessException;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.sansec.ai.user.constant.ErrorCodeConst.USER_LOCK_CNT_ERROR;
import static com.sansec.ai.user.constant.ErrorCodeConst.USER_LOCK_RELEASE_ERROR;


/**
 * @Author：cuipengfei
 * @CreateAt: 2022-05-20  14:31
 * @Description: 用户锁定实现
 */
@Service
public class PortalUserLockServiceImpl implements PortalUserLockService {

    @Autowired
    Cache<String, UserLock> portalLockAccountCache;

    @Value("${ai.user.login.enableLock:1}")
    private String enableLock;

    @Value("${ai.user.login.lockCnt:5}")
    private Integer lockCnt;

    @Value("${ai.user.login.imageCodeCnt:3}")
    private Integer imageCodeCnt;


    @Value("${ai.user.login.lockTime:60}")
    private Integer lockTime;


    @Override
    public void getFlagLock(String userName) {

        UserLock userLock = portalLockAccountCache.getIfPresent(userName);

        if (userLock == null) {
            return;
        }

        if (FLagLockEnum.LOCK.getCode().equals(enableLock)) {
            Date nowDate = new Date();
            if (userLock.getLockCnt() >= lockCnt) {
                if (userLock.getDate() == null) {
                    userLock.setDate(new Date().getTime() + lockTime * 1000);
                }
                if (userLock.getDate() > nowDate.getTime()) {
                    long releaseTime = (userLock.getDate() - nowDate.getTime()) / 1000;
                    releaseTime = releaseTime == 0 ? 1 : releaseTime;
                    throw new BusinessException(USER_LOCK_RELEASE_ERROR, true, releaseTime);
                } else {
                    portalLockAccountCache.invalidate(userName);
                }
            }
        }
    }

    @Override
    public void lockUser(String userName) {
        UserLock userLock = portalLockAccountCache.getIfPresent(userName);

        if (!FLagLockEnum.LOCK.getCode().equals(enableLock)) {
            return;
        }

        if (ObjectUtils.isEmpty(userLock)) {
            userLock = new UserLock();
            userLock.setUserName(userName);
            userLock.setLockCnt(1);
            if (userLock.getLockCnt() >= lockCnt) {
                userLock.setDate(new Date().getTime() + lockTime * 1000);
            }
        } else {
            userLock.setLockCnt(userLock.getLockCnt() + 1);
            if (userLock.getLockCnt() >= lockCnt) {
                userLock.setDate(new Date().getTime() + lockTime * 1000);
            }
        }
        portalLockAccountCache.put(userName, userLock);

        if (userLock.getLockCnt() >= lockCnt) {
            throw new BusinessException(USER_LOCK_RELEASE_ERROR, true, lockTime);
        }
        throw new BusinessException(USER_LOCK_CNT_ERROR, true, lockCnt - userLock.getLockCnt());
    }

    @Override
    public void release(String userName) {
        portalLockAccountCache.invalidate(userName);
    }

    @Override
    public boolean isImageCodeDisplay(String userName) {
        UserLock userLock = portalLockAccountCache.getIfPresent(userName);
        if (userLock == null) {
            return false;
        }
        return userLock.getLockCnt() >= imageCodeCnt;
    }
}
