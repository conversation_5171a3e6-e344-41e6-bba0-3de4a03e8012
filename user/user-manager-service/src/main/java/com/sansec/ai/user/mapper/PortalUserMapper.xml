<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sansec.ai.user.mapper.PortalUserMapper">
	<sql id="Base_Column_List">
		id, user_name, user_pwd, login_date, remark, create_time, update_time, create_by, update_by, flag_del,
		source_type, alias_name
	</sql>

	<select id="getPagedPortalUserList" resultType="com.sansec.ai.user.entity.vo.PortalUserPageVO">
		select
			u.id,
			u.user_name,
			u.source_type,
			u.alias_name,
			u.login_date,
			rel.portal_id AS portalId
		from portal_user u
		left join portal_user_relation rel on rel.user_id = u.id and rel.flag_del=0 and rel.user_type = 'normal'
		<where>
			u.flag_del = 0
			<if test="param.userName != null and param.userName != ''">
				<bind name="userName" value="'%'+param.userName+'%'"/>
				and u.user_name LIKE #{userName}
			</if>
			<if test="param.portalId != null">
				and rel.portal_id = #{param.portalId}
			</if>
			<choose>
				<when test="param.bindStatus != null and param.bindStatus == 'true'">
					rel.portal_id > 0
				</when>
				<when test="param.bindStatus != null and param.bindStatus == 'false'">
					rel.portal_id is null
				</when>
			</choose>
		</where>
		order by u.login_date desc
	</select>

</mapper>