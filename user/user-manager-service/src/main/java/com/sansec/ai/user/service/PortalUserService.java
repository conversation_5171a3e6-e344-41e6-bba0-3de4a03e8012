package com.sansec.ai.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sansec.ai.user.entity.request.PortalUserAddDTO;
import com.sansec.ai.user.entity.request.PortalUserIdsDTO;
import com.sansec.ai.user.entity.request.PortalUserPageDTO;
import com.sansec.ai.user.entity.po.PortalUser;
import com.sansec.ai.user.entity.request.PortalUserModifyDTO;
import com.sansec.ai.user.entity.vo.PortalUserPageVO;
import com.sansec.ai.user.entity.vo.PortalUserVO;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;


/**
 * @Author: WanJun
 * @Date: 2024/10/24 10:20
 * @Description: 用户管理接口
 */
public interface PortalUserService extends IService<PortalUser> {

	Long add(PortalUserAddDTO dto);

	void modify(PortalUserModifyDTO dto);

	void delete(PortalUserIdsDTO dto);

	SecPageVO<PortalUserPageVO> page(PortalUserPageDTO dto);

	/**
	 * 查询所有用户
	 * @param dto
	 * @return
	 */
	SecPageVO<PortalUserPageVO> pageAll(PortalUserPageDTO dto);

	SecRestResponse<String> bind(PortalUserIdsDTO dto);

	SecRestResponse<String> unbind(PortalUserIdsDTO dto);

	PortalUserVO get(Long userId);
}
