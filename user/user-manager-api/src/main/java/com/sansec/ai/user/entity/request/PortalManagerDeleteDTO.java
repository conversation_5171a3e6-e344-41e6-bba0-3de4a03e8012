package com.sansec.ai.user.entity.request;

import com.sansec.ai.common.base.paramverify.Verify;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: WanJun
 * @Date: 2025/5/12 09:08
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PortalManagerDeleteDTO {

    /**
     * 门户id
     */
    @Verify(notNull = true)
    private Long portalId;

    /**
     * 是否新增用户
     */
    @Verify(notNull = true)
    private Boolean deleteUser;

    private String secToken;
}