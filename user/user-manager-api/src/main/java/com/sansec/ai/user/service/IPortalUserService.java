package com.sansec.ai.user.service;

import com.sansec.ai.common.base.entity.ImageCode;
import com.sansec.ai.user.constant.PortalLoginResEnum;
import com.sansec.ai.user.constant.UserTypeEnum;
import com.sansec.ai.user.entity.request.CaptchaGetDTO;
import com.sansec.ai.user.entity.request.PortalUserAddDTO;
import com.sansec.ai.user.entity.request.PortalUserLoginDTO;
import com.sansec.ai.user.entity.request.PortalUserModifyDTO;
import com.sansec.ai.user.entity.vo.CaptchaGetVO;
import com.sansec.ai.user.entity.vo.PortalUserVO;
import com.sansec.ai.user.entity.vo.UserStatusVO;

/**
 * @Author: WanJun
 * @Date: 2025/5/12 08:39
 * @Description:
 */
public interface IPortalUserService {

	/**
	 * 获取门户用户信息
	 *
	 * @param userId 用户id
	 * @return 用户信息
	 */
	PortalUserVO get(Long userId);

	/**
	 * 获取门户用户信息
	 *
	 * @param userName
	 * @param portalId
	 * @return 用户信息
	 */
	PortalUserVO getByUserName(String userName, Long portalId);

	/**
	 * 生成图片验证码
	 *
	 * @return 返回图片验证码id
	 */
	CaptchaGetVO generateCaptcha();

	/**
	 * 根据验证码id获取验证码
	 *
	 * @param captchaGetDTO 验证码id
	 * @return 图片验证码写入响应
	 */

	ImageCode getCaptcha(CaptchaGetDTO captchaGetDTO);

	/**
	 * 用户登录
	 *
	 * @param dto
	 * @return 登录成功返回token
	 */
	String login(PortalUserLoginDTO dto);

	/**
	 * 三方用户登录
	 *
	 * @param userId
	 * @param userType
	 * @param portalCode
	 * @return
	 */
	String thirdLogin(Long userId, UserTypeEnum userType, String portalCode);

	/**
	 * 退出登录
	 */
	void logout();

	PortalLoginResEnum checkToken(String token, String requestUri);

	UserStatusVO getUserStatus(String userName);

	void modify(PortalUserModifyDTO dto);

	Long add(PortalUserAddDTO dto);
}
