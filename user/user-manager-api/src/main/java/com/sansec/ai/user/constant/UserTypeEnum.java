package com.sansec.ai.user.constant;

/**
 * 用户身份类型
 *
 * <AUTHOR>
 * @since 2025/5/12 13:43
 */
public enum UserTypeEnum {

	/**
	 * 系统管理员
	 */
	SYSTEM_MANAGER(10, "manager", "系统管理员"),
	/**
	 * 门户管理员
	 */
	PORTAL_MANAGER(20, "manager", "门户管理员"),
	/**
	 * 门户用户
	 */
	PORTAL_USER(30, "normal", "门户用户"),
	;

	public final int code;
	public final String type;
	public final String description;

	UserTypeEnum(int code, String type, String description) {
		this.code = code;
		this.type = type;
		this.description = description;
	}

	public int getCode() {
		return code;
	}

	public String getDescription() {
		return description;
	}

	public static UserTypeEnum getEnum(Integer code) {
		if (code == null) {
			return null;
		}
		for (UserTypeEnum typeEnum : UserTypeEnum.values()) {
			if (typeEnum.code == code) {
				return typeEnum;
			}
		}
		return null;
	}

}
