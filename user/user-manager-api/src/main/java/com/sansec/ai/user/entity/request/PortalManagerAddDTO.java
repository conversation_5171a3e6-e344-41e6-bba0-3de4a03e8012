package com.sansec.ai.user.entity.request;

import com.sansec.ai.common.base.constant.FieldRegex;
import com.sansec.ai.common.base.paramverify.Verify;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: WanJun
 * @Date: 2025/5/12 08:46
 * @Description:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PortalManagerAddDTO {

    /**
     * 用户名
     */
    @Verify(regular = FieldRegex.USER_NAME_REGEX)
    private String userName;


    /**
     * 用户口令
     */
    private String userPwd;


    /**
     * 门户id
     */
    private Long portalId;

    /**
     * 是否新增用户
     */
    @Verify(notNull = true)
    private Boolean addNewUser;

    /**
     * 用户id，当addNewUser为true时必填
     */
    private String userId;

    /**
     * 用户类型，使用UserTypeEnum 即可
     */
    private String userType;

    private String secToken;
}