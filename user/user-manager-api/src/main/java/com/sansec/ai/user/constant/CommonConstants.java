package com.sansec.ai.user.constant;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2025/5/17 13:52
 * @Description:
 */
public class CommonConstants {

    public static final String COOKIE_NAME = "sansec-ai-token";


    public static final String PLACE_HOLDER = "PLACEHOLDER";

    public static final String PREDICATE_TEMPLATE_WITH_COOKIE = "[{\"name\":\"Path\",\"args\":{\"pattern\":\"/**\"}},{\"name\":\"CookieCheck\",\"args\":{\"expectedValue\":\"PLACEHOLDER\"}}]";
    public static final String PREDICATE_TEMPLATE = "[{\"name\":\"Path\",\"args\":{\"pattern\":\"/**\"}}]";

//    ,{"name":"StripPrefix","args":{"parts":"1"}
    public static final String FILTER_TEMPLATE = "[{\"name\":\"<PERSON>ieCheck\",\"args\":{\"expectedValue\":\"PLACEHOLDER\"}}]";

    public static final String FILTER_ALL_REQUEST_PATTERN = "/**";

    public static final Integer ROUTE_ORDER = 100;

    public static final String PORTAL_MANAGER_CODE = "oper";

    public static final Integer NAME_PWD_LOGIN_USER = 1;

    public static final String DEFAULT_AUTH_CODE="Swxa1234.";
}