package com.sansec.ai.user.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: WanJun
 * @Date: 2025/5/12 10:51
 * @Description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PortalUserPageVO {
    /**
     * 用户id
     */
    private Long id;
    /**
     * 用户名称
     */
    private String userName;
	/**
	 * 用户来源**1-系统用户 2-LDAP用户**
	 */
	private Integer sourceType;
	/**
	 * 用户别名
	 */
	private String aliasName;
    /**
     * 门户id
     */
    private Long portalId;

    /**
     * 绑定状态
     */
    private Boolean bindStatus;

    /**
     * 门户名称
     */
    private String portalName;

    /**
     * 登录时间
     */
    private Date loginDate;

    private Boolean enableBind;
}