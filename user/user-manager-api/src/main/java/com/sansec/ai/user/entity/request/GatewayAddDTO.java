package com.sansec.ai.user.entity.request;

import lombok.Data;

@Data
public class GatewayAddDTO {
    /**
     * 路由id，对应门户id或者门户名称或者应用id，需要保证唯一性
     */
    private String routeId;

    /**
     * 路由uri，对应protocol://ip:port
     */
    private String uri;

    /**
     * 请求前缀，用于拦截请求
     */
//    private String requestPrefix;

    /**
     * 匹配的cookie值，用于根据cookie判断是否转发
     */
    private String cookieValue;

//    /**
//     * 路由predicates，对应请求路径前缀
//     */
//    private String predicates;

//    /**
//     * 路由filters，对应过滤器
//     */
//    private String filters;
} 