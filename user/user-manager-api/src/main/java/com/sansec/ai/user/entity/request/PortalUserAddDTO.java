package com.sansec.ai.user.entity.request;

import com.sansec.ai.common.base.constant.FieldRegex;
import com.sansec.ai.common.base.paramverify.Verify;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @Author: WanJun
 * @Date: 2025/5/12 08:46
 * @Description:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PortalUserAddDTO {

	/**
	 * 用户名
	 */
	@Verify(regular = FieldRegex.USER_NAME_REGEX, notBlank = true)
	private String userName;
	/**
	 * 用户来源**1-系统用户 2-LDAP用户**
	 */
	private Integer sourceType;
	/**
	 * 用户别名
	 */
	private String aliasName;
	/**
	 * 用户口令
	 */
	@Verify(regular = FieldRegex.USER_AUTH_CODE_REGEX, notBlank = true)
	private String userPwd;
	/**
	 * 门户id
	 */
	@Verify(notNull = true)
	private Long portalId;



}