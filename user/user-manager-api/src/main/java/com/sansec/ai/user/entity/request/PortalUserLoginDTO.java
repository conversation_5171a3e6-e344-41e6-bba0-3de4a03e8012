package com.sansec.ai.user.entity.request;

import com.sansec.ai.common.base.paramverify.Verify;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: WanJun
 * @Date: 2024/10/23 17:22
 * @Description: 用户登录请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PortalUserLoginDTO {
    /**
     * 用户名
     */
    @Verify(notBlank = true)
    private String userName;

    /**
     * 密码
     */
    @Verify(notBlank = true)
    private String userPwd;

    /**
     * 验证码id
     */
    private String captchaId;

    /**
     * 验证码
     */
    private String captcha;
}