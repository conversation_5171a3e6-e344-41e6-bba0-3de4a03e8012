package com.sansec.ai.user.constant;

/**
 * @Author: Wan<PERSON><PERSON>
 * @Date: 2024/10/24 09:28
 * @Description: 用户管理模块错误码常量类
 * 00050000~0050FFFF
 * 租户相关的错误码：00050000~00050FFF
 * 用户相关的错误码：00051000~00051FFF
 */
public interface ErrorCodeConst {

    // 网关模块的相关错误码
    String ROUTE_ID_REPEAT = "00060A01";


    // 用户模块的相关错误码
    String USER_CAPTCHA_ID_NOT_BE_BLANK = "00060A02";
    String USER_CAPTCHA_CODE_EXPIRED = "00060A03";
    String USER_CAPTCHA_CODE_WRITE_TO_RESPONSE_ERROR = "00060A04";
    String USER_CAPTCHA_CODE_VERIFY_ERROR = "00060A05";
    String USER_AUTH_CODE_NOT_CORRECT_ERROR = "00060A06";
    String USER_NAME_ALREADY_EXIST = "00060A07";
    String PORTAL_MANAGER_BIND_PORTAL_ERROR = "00060A08";
    String SOME_USER_ALREADY_BIND_PORTAL = "00060A09";
	/**
	 * 只能绑定到管理员所属门户
	 */
	String PORTAL_MANAGER_NOT_MATCH = "00060A10";

    String USER_NOT_EXISTS = "00060A0A";

    /**
     * 用户锁定次数提示
     */
    String USER_LOCK_CNT_ERROR = "00060A0B";
    /**
     * 用户还有多久释放提示
     */
    String USER_LOCK_RELEASE_ERROR = "00060A0C";
    String OLD_USER_AUTH_CODE_CANNOT_BE_EMPTY = "00060A0D";
    String OLD_USER_AUTH_CODE_NOT_CORRECT_ERROR = "00060A0E";
    String NEW_USER_AUTH_CODE_SAME_WITH_OLD_ERROR = "00060A0F";
    String USER_NOT_BIND_PORTAL = "00060A11";
	/**
	 * 未查询到门户信息，请检查门户地址是否正确
	 */
	String LOGIN_PORTAL_CODE_NOT_EXIST = "00060A12";
	/**
	 * 您无权访问当前门户，请检查门户地址，或联系门户管理员处理
	 */
	String LOGIN_PORTAL_USER_NOT_MATCH = "00060A13";
	/**
	 * 认证失败，账号或密码错误
	 */
	String LOGIN_LDAP_AUTH_ERROR = "00060A14";
	/**
	 * 未找到当前用户的组织信息
	 */
	String LOGIN_LDAP_ORG_NOT_EXIST = "00060A15";
	/**
	 * 您的注册渠道不是AD域，请选择其它方式登录
	 */
	String LOGIN_LDAP_USER_NOT_LDAP = "00060A16";
	/**
	 * 您无权访问当前门户，是否跳转到您对应的门户[%s]？
	 */
	String LOGIN_LDAP_PORTAL_NOT_MATCH = "00060A17";

}