package com.sansec.ai.user.entity.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author: WanJun
 * @Date: 2025/5/12 09:30
 * @Description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PortalUserVO {


	/**
	 * 用户id
	 */
	private Long id;
	/**
	 * 用户名
	 */
	private String userName;
	/**
	 * 门户id
	 */
	private Long portalId;
	/**
	 * 最后登录时间
	 */
	private Date loginDate;
	/**
	 * 用户来源**1-系统用户 2-LDAP用户**
	 */
	private Integer sourceType;
	/**
	 * 用户别名
	 */
	private String aliasName;
}