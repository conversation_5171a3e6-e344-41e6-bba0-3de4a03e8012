package com.sansec.ai.user.entity.request;

import com.sansec.ai.common.base.constant.FieldRegex;
import com.sansec.ai.common.base.paramverify.Verify;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: Wan<PERSON><PERSON>
 * @Date: 2025/5/12 08:46
 * @Description:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PortalUserModifyDTO {
	/**
	 * 用户口令
	 */
	@Verify(regular = FieldRegex.USER_AUTH_CODE_REGEX, notBlank = true)
	private String userPwd;


	/**
	 * 用户id
	 */
	@Verify(notNull = true)
	private Long userId;

	private String oldUserPwd;
}