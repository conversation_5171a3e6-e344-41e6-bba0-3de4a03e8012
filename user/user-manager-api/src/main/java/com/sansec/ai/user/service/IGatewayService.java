package com.sansec.ai.user.service;

import com.sansec.ai.user.entity.request.GatewayAddDTO;

/**
 * @Author: Wan<PERSON><PERSON>
 * @Date: 2025/5/12 09:35
 * @Description:
 */
public interface IGatewayService {

    /**
     * 添加网关
     * @param dto 请求
     * @return id
     */
    Long add(GatewayAddDTO dto);

    /**
     * 根据门户id删除路由
     *
     * @param routeId 路由id，对应门户id或门户名称
     * @return 路由id
     */
    Long delete(String routeId);
}
