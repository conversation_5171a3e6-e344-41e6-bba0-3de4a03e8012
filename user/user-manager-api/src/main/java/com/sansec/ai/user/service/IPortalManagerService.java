package com.sansec.ai.user.service;

import com.sansec.ai.user.entity.request.PortalManagerAddDTO;
import com.sansec.ai.user.entity.request.PortalManagerDeleteDTO;
import com.sansec.ai.user.entity.request.PortalManagerListDTO;
import com.sansec.ai.user.entity.vo.PortalManagerVO;

import java.util.List;

/**
 * @Author: WanJun
 * @Date: 2025/5/12 08:39
 * @Description:
 */
public interface IPortalManagerService {

    /**
     * 新增门户管理员
     * @param dto 请求信息
     * @return 响应信息
     */
    Long add(PortalManagerAddDTO dto);


    /**
     * 删除门户管理员
     *
     * @param dto 请求信息
     * @return 响应，门户管理员id
     */
    Long delete(PortalManagerDeleteDTO dto);

    /**
     * 查询门户管理员列表
     *
     * @return 门户管理员列表
     */
    List<PortalManagerVO> list(PortalManagerListDTO dto);

	/**
	 * 根据门户ID查询门户管理员
	 *
	 * @param portalId 门户ID
	 * @return 门户管理员信息
	 */
	PortalManagerVO getByPortalId(Long portalId);

    /**
     * 根据id查询门户管理员
     *
     * @param userId 管理员id
     * @return 门户管理员信息
     */
    PortalManagerVO get(Long userId);

    /**
     * 校验用户名称是否存在
     *
     * @param userName 用户名称
     * @return true:存在 false:不存在
     */
    Boolean checkManagerExist(String userName);
}
