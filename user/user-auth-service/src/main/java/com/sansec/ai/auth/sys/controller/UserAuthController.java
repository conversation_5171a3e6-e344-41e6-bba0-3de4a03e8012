package com.sansec.ai.auth.sys.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.sansec.ai.common.base.entity.ImageCode;
import com.sansec.ai.common.base.enums.TokenTypeEnum;
import com.sansec.ai.common.base.utils.TokenUtil;
import com.sansec.ai.user.entity.request.CaptchaGetDTO;
import com.sansec.ai.user.entity.request.PortalUserLoginDTO;
import com.sansec.ai.user.entity.request.PortalUserModifyDTO;
import com.sansec.ai.user.entity.vo.CaptchaGetVO;
import com.sansec.ai.user.entity.vo.PortalUserVO;
import com.sansec.ai.user.entity.vo.UserStatusVO;
import com.sansec.ai.user.service.IPortalUserService;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecRestResponse;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import java.io.IOException;

import static com.sansec.ai.user.constant.ErrorCodeConst.OLD_USER_AUTH_CODE_CANNOT_BE_EMPTY;
import static com.sansec.ai.user.constant.ErrorCodeConst.USER_CAPTCHA_CODE_WRITE_TO_RESPONSE_ERROR;

/**
 * @Author: WanJun
 * @Date: 2025/5/19 09:39
 * @Description:
 */

/**
 * 用户认证
 */
@Slf4j
@RestController
@RequestMapping(value = "/hub/user/")
public class UserAuthController {

	@Resource
	private IPortalUserService userService;

	/**
	 * 登录
	 *
	 * @param dto 用户登录请求
	 * @return 用户id
	 */
	@PostMapping("login")
	public SecRestResponse<String> login(@RequestBody PortalUserLoginDTO dto, HttpServletResponse response) {
		return SecRestResponse.success(userService.login(dto));
	}


	@GetMapping(value = "/getUserStatus")
	public SecRestResponse<UserStatusVO> getUserStatus(@RequestParam("userName") String userName) {
		UserStatusVO vo = userService.getUserStatus(userName);
		return SecRestResponse.success(vo);
	}

	/**
	 * 生成图片二维码
	 *
	 * @return 二维码
	 */
	@PostMapping("captcha/image/generate")
	public SecRestResponse<CaptchaGetVO> generateCaptcha() {
		return SecRestResponse.success(userService.generateCaptcha());
	}

	/**
	 * 根据验证码id获取验证码
	 *
	 * @param captchaGetDTO 请求
	 * @param response      响应
	 * @return 返回
	 */
	@PostMapping("captcha/image/get")
	public SecRestResponse<String> getCaptcha(@RequestBody CaptchaGetDTO captchaGetDTO, HttpServletResponse response) {
		ImageCode imageCode = userService.getCaptcha(captchaGetDTO);

		response.setContentType(MediaType.IMAGE_PNG_VALUE);
		try (ServletOutputStream outputStream = response.getOutputStream()) {
			ImageIO.write(imageCode.getImage(), "png", outputStream);
		} catch (IOException e) {
			log.error("verifyCodeCache error", e);
			throw new BusinessException(USER_CAPTCHA_CODE_WRITE_TO_RESPONSE_ERROR);
		}
		return SecRestResponse.success();
	}

	/**
	 * 登出
	 *
	 * @return 无
	 */
	@PostMapping("logout")
	public SecRestResponse<String> logout() {
		userService.logout();
		return SecRestResponse.success();
	}


	/**
	 * 获取当前用户信息
	 *
	 * @return
	 */
	@GetMapping(value = "getCurrentUser")
	public SecRestResponse<PortalUserVO> getCurrentUser() {
		Long userId = TokenUtil.getUserId(TokenTypeEnum.AI_PORTAL_TOKEN);
		return SecRestResponse.success(userService.get(userId));
	}

	/**
	 * 修改用户密码
	 *
	 * @param dto 用户修改密码请求
	 * @return 用户id
	 */
	@PostMapping("changePwd")
	public SecRestResponse<String> modify(@RequestBody PortalUserModifyDTO dto) {
		if (StringUtils.isEmpty(dto.getOldUserPwd())) {
			throw new BusinessException(OLD_USER_AUTH_CODE_CANNOT_BE_EMPTY);
		}
		userService.modify(dto);
		return SecRestResponse.success(String.valueOf(dto.getUserId()));
	}
}