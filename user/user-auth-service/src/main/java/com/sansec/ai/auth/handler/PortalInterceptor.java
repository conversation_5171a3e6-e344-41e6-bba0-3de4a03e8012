package com.sansec.ai.auth.handler;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.sansec.ai.common.base.constant.RequestAttributeConst;
import com.sansec.ai.common.base.constant.ErrorCode;
import com.sansec.ai.common.base.handler.BaseInterceptorAdapter;
import com.sansec.ai.user.constant.PortalLoginResEnum;
import com.sansec.ai.user.constant.UserTypeEnum;
import com.sansec.ai.user.service.IPortalUserService;
import com.sansec.ai.user.entity.vo.PortalUserVO;
import com.sansec.common.exception.BusinessException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 统一门户拦截器
 *
 * <AUTHOR>
 * @since 2025/5/12 11:55
 */
@Slf4j
@Component
public class PortalInterceptor extends BaseInterceptorAdapter {

	@Value("${secplat.base.token}")
	private String tokenKey;
	/**
	 * 统一门户Cookie
	 */
	@Value("${ai.portal.cookie-name}")
	private String cookieName;

	@Autowired
	private IPortalUserService userService;

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
		//解析token
		String token = request.getHeader("AiPortalToken");
		if (StringUtils.isBlank(token)) {
			throw new BusinessException(ErrorCode.AUTH_VERIFY_FAILED);
		}
		if (!JWTUtil.verify(token, tokenKey.getBytes())) {
			throw new BusinessException(ErrorCode.AUTH_VERIFY_FAILED);
		}
		//NOTE 1-检查用户ID和过期时间
		if (userService.checkToken(token, request.getRequestURI()) != PortalLoginResEnum.SUCCESS) {
			throw new BusinessException(ErrorCode.AUTH_VERIFY_FAILED);
		}

		//NOTE 2-检查门户是否匹配
		JWT jwt = JWTUtil.parseToken(token);
		//token中携带的门户code
		String portalCodeInToken = String.valueOf(jwt.getPayload().getClaim("portalCode"));
		if(StringUtils.isBlank(portalCodeInToken)){
			throw new BusinessException(ErrorCode.AUTH_VERIFY_FAILED);
		}
		//Cookie中携带的门户code
		String portalCodeInCookie = null;
		for (Cookie cookie : request.getCookies()) {
			if (!cookie.getName().equals(cookieName)) {
				continue;
			}
			portalCodeInCookie = cookie.getValue();
		}
		//门户code不匹配
		if(portalCodeInCookie == null){
			throw new BusinessException(ErrorCode.AUTH_VERIFY_FAILED);
		}
		if(!portalCodeInCookie.equals(portalCodeInToken)){
			throw new BusinessException(ErrorCode.AUTH_VERIFY_FAILED);
		}
		//NOTE 3-用户信息向下传递
		//门户code填充到request中
		request.setAttribute(RequestAttributeConst.PORTAL_CODE, portalCodeInCookie);
		//获取用户ID，填充到request中
		Long userId = NumberUtil.parseLong(String.valueOf(jwt.getPayload().getClaim("userId")));
		request.setAttribute(RequestAttributeConst.USER_ID, userId);
		//获取用户信息
		PortalUserVO userInfo = userService.get(userId);
		//当前用户未绑定门户
		if(userInfo.getPortalId() == null){
			throw new BusinessException(ErrorCode.AUTH_VERIFY_FAILED);
		}
		//门户ID填充到request中
		request.setAttribute(RequestAttributeConst.PORTAL_ID, userInfo.getPortalId());
		//用户类型填充到request中
		request.setAttribute(RequestAttributeConst.USER_TYPE, UserTypeEnum.PORTAL_USER);
		return true;
	}
}
