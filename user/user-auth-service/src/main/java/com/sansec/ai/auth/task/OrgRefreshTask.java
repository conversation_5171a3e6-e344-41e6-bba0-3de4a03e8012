package com.sansec.ai.auth.task;

import com.sansec.ai.auth.ldap.config.LdapConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 定时刷新组织结构
 *
 * <AUTHOR>
 * @since 2025/7/2 13:58
 */
@Slf4j
@Component
public class OrgRefreshTask {

	@Autowired
	private LdapConfiguration ldapConfiguration;

	/**
	 * 定时刷新组织结构，每1min执行一次
	 */
	@Scheduled(initialDelay = 60 * 1000L, fixedDelay = 60 * 1000L)
	public void run() {
		log.info("定时刷新组织结构-开始");
		ldapConfiguration.reloadOrgPortalMap();
		log.info("定时刷新组织结构-结束");
	}

}
