package com.sansec.ai.auth.controller;

import com.sansec.ai.common.base.constant.RequestAttributeConst;
import com.sansec.ai.user.constant.UserTypeEnum;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * <AUTHOR>
 * @since 2025/5/12 11:14
 */
public abstract class BaseController {

	/**
	 * 获取用户类型
	 *
	 * @return
	 */
	protected UserTypeEnum getUserType() {
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (attributes == null) {
			return null;
		}

		return (UserTypeEnum) attributes.getRequest().getAttribute(RequestAttributeConst.USER_TYPE);
	}

	/**
	 * 获取用户ID
	 *
	 * @return
	 */
	protected Long getUserId() {
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (attributes == null) {
			return null;
		}
		return (Long) attributes.getRequest().getAttribute(RequestAttributeConst.USER_ID);
	}

	/**
	 * 获取门户ID
	 *
	 * @return
	 */
	protected Long getPortalId() {
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (attributes == null) {
			return null;
		}
		return (Long) attributes.getRequest().getAttribute(RequestAttributeConst.PORTAL_ID);
	}

}
