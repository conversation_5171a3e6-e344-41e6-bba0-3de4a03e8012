package com.sansec.ai.auth.ldap.service;

import com.sansec.ai.auth.ldap.entity.LdapUser;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.filter.EqualsFilter;
import org.springframework.stereotype.Service;

import java.util.List;

import static org.springframework.ldap.query.LdapQueryBuilder.query;

@Slf4j
@Service("ILdapService")
public class LdapServiceImpl implements ILdapService {


	@Resource
	private LdapTemplate ldapTemplate;
	/**
	 * 用作本系统中用户登录ID的LDAP属性
	 */
	@Value("${spring.ldap.login_attr}")
	private String loginAttr;

	/**
	 * LDAP用户认证
	 *
	 * @param loginName
	 * @param password
	 * @return
	 */
	@Override
	public boolean authenticate(String loginName, String password) {
		EqualsFilter filter = new EqualsFilter(loginAttr, loginName);
		return ldapTemplate.authenticate("", filter.toString(), password);
	}

	/**
	 * 获取LDAP用户信息
	 *
	 * @param loginName
	 * @return
	 */
	@Override
	public LdapUser getLdapUser(String loginName) {
//		keyword = "*" + keyword + "*";
//		// 检索域用户(根据用户登录名、正式名称、邮箱，模糊搜索)
//		// sAMAccountName：用户登录名（Windows 2000以前版本，以后的版本是userPrincipalName）
//		LdapQuery query = query().where("sAMAccountName").like(keyword).or("cn").like(keyword).or("mail").like(keyword);
//		return ldapTemplate.find(query, LdapUser.class);
		return ldapTemplate.findOne(query().where(loginAttr).is(loginName), LdapUser.class);
	}


	@Override
	public List<LdapUser> getAll() {
		return ldapTemplate.findAll(LdapUser.class);
	}
}