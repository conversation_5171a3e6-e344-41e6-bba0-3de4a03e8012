package com.sansec.ai.auth.ldap.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import io.micrometer.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/6/30 11:01
 */
@Slf4j
@Configuration
@EnableConfigurationProperties()
@ConditionalOnProperty(name = "spring.ldap.enable", havingValue = "true", matchIfMissing = false)
public class LdapConfiguration implements InitializingBean {

	@jakarta.annotation.Resource
	private ApplicationContext context;
	@Value("${spring.ldap.org-path}")
	private String orgPath;

	/**
	 * 组织和门户的关联关系<br>
	 * key=LDAP中的组织名, value=PortalCode
	 */
	private Map<String, String> orgPortalMap = new HashMap<>();

	@Override
	public void afterPropertiesSet() throws Exception {
		Resource resource = context.getResource(orgPath);
		if (!resource.exists()) {
			throw new NullPointerException(String.format("未找到组织结构配置文件[%s]", orgPath));
		}
		//组织结构
		JSONObject orgJson;
		try {
			orgJson = JSON.parseObject(resource.getInputStream(), StandardCharsets.UTF_8, JSONObject.class,
					// 自动关闭流
					Feature.AutoCloseSource,
					// 允许注释
					Feature.AllowComment,
					// 允许单引号
					Feature.AllowSingleQuotes,
					// 使用 Big decimal
					Feature.UseBigDecimal);
		} catch (Exception e) {
			log.error("组织结构配置文件解析失败", e);
			SpringApplication.exit(context);
			return;
		}
		if (orgJson.isEmpty()) {
			log.error("组织结构配置文件解析失败[org={}]", orgJson);
			SpringApplication.exit(context);
			return;
		}
		//根据组织结构，构建门户和组织名的关联关系
		orgPortalMap = decodeOrganization(orgJson, null);
	}

	/**
	 * 根据组织名，获取关联的门户
	 * @param orgName
	 * @return
	 */
	public String getPortalCode(String orgName) {
		return orgPortalMap.get(orgName);
	}

	/**
	 * 重新加载组织结构，适用于动态修改组织结构配置文件
	 */
	public void reloadOrgPortalMap() {
		Resource resource = context.getResource(orgPath);
		if (!resource.exists()) {
			throw new NullPointerException(String.format("未找到组织结构配置文件[%s]", orgPath));
		}
		//组织结构
		JSONObject orgJson;
		try {
			orgJson = JSON.parseObject(resource.getInputStream(), StandardCharsets.UTF_8, JSONObject.class,
					// 自动关闭流
					Feature.AutoCloseSource,
					// 允许注释
					Feature.AllowComment,
					// 允许单引号
					Feature.AllowSingleQuotes,
					// 使用 Big decimal
					Feature.UseBigDecimal);
		} catch (Exception e) {
			log.error("组织结构配置文件解析失败", e);
			return;
		}
		if (orgJson.isEmpty()) {
			log.error("组织结构配置文件解析失败[org={}]", orgJson);
			return;
		}
		//根据组织结构，构建门户和组织名的关联关系
		Map<String, String> newOrgPortalMap = decodeOrganization(orgJson, null);
		if (newOrgPortalMap != null) {
			orgPortalMap = newOrgPortalMap;
		}
	}

	/**
	 * 解析组织结构
	 *
	 * @param orgJson          组织结构JSON
	 * @param parentPortalCode 父组织关联的门户
	 * @return 组织和门户的关联关系：key=LDAP中的组织名, value=PortalCode
	 */
	private Map<String, String> decodeOrganization(JSONObject orgJson, String parentPortalCode) {
		//组织名
		String orgName = orgJson.getString("orgName");
		if (StringUtils.isBlank(orgName)) {
			return null;
		}
		//如果关联门户为空，则默认关联父组织关联的门户
		String portalCode = orgJson.getString("portalCode");
		if (StringUtils.isBlank(portalCode)) {
			portalCode = parentPortalCode;
		}
		//组织和门户的关联关系
		Map<String, String> portalOrgMap = new HashMap<>();
		portalOrgMap.put(orgName, portalCode);

		//获取子组织
		JSONArray children = orgJson.getJSONArray("subList");
		if (children == null || children.isEmpty()) {
			return portalOrgMap;
		}
		for (int i = 0; i < children.size(); i++) {
			Map<String, String> map = decodeOrganization(children.getJSONObject(i), portalCode);
			if (map == null) {
				continue;
			}
			portalOrgMap.putAll(map);
		}

		return portalOrgMap;
	}

}
