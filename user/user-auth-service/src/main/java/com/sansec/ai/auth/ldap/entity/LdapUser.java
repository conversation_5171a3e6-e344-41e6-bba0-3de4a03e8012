package com.sansec.ai.auth.ldap.entity;

import io.micrometer.common.util.StringUtils;
import lombok.Data;
import org.springframework.ldap.odm.annotations.Attribute;
import org.springframework.ldap.odm.annotations.Entry;
import org.springframework.ldap.odm.annotations.Id;

import javax.naming.Name;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Entry(objectClasses = {"user", "organizationalPerson", "top", "Person"})
@Data
public class LdapUser {
	@Id
	private Name dn;
	@Attribute(name = "sAMAccountName")
	private String loginName;
	@Attribute(name = "userPrincipalName")
	private String email;
	@Attribute(name = "distinguishedName")
	private String distinguishedName;
	/**
	 * 正式名称，即用户姓
	 */
	@Attribute(name = "cn")
	private String userName;

	/**
	 * 获取所属组织列表
	 *
	 * @return
	 */
	public List<String> getOrgNameList() {
		if (StringUtils.isBlank(distinguishedName)) {
			return null;
		}
		List<String> orgNameList = new ArrayList<>();
		// 正则表达式匹配 "OU=xxx" 的部分（不区分大小写）
		Pattern pattern = Pattern.compile("(?i)OU=([^,]+)");
		Matcher matcher = pattern.matcher(distinguishedName);

		while (matcher.find()) {
			String ouValue = matcher.group(1).trim();
			orgNameList.add(ouValue);
		}

		return orgNameList;
	}

}