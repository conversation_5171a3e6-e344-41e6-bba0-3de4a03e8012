package com.sansec.ai.auth.ldap.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.sansec.ai.auth.ldap.config.LdapConfiguration;
import com.sansec.ai.auth.ldap.entity.LdapUser;
import com.sansec.ai.auth.ldap.service.ILdapService;
import com.sansec.ai.portal.entity.vo.PortalSiteInfoVo;
import com.sansec.ai.portal.service.IPortalService;
import com.sansec.ai.user.constant.ErrorCodeConst;
import com.sansec.ai.user.constant.UserTypeEnum;
import com.sansec.ai.user.entity.request.PortalUserAddDTO;
import com.sansec.ai.user.entity.request.PortalUserLoginDTO;
import com.sansec.ai.user.entity.vo.PortalUserVO;
import com.sansec.ai.user.service.IPortalUserService;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.SM3Util;
import jakarta.annotation.Resource;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.*;

/**
 * LDAP用户认证
 *
 * <AUTHOR>
 * @since 2025/5/12 11:14
 */
@Slf4j
@RestController
@ConditionalOnProperty(name = "spring.ldap.enable", havingValue = "true", matchIfMissing = false)
@RequestMapping(value = "/hub/ldap/")
public class LdapAuthController {


	@Autowired
	private ILdapService ldapService;

	@Value("${ai.portal.cookie-name}")
	private String cookieName;
	/**
	 * 统一门户重定向地址
	 */
	@Value("${ai.portal.redirect-url}")
	private String portalRedirectUrl;
	/**
	 * 根组织绑定的门户
	 */
	@Value("${spring.ldap.root-portal-code:sansec}")
	private String rootPortalCode;
	@Resource
	private IPortalUserService portalUserService;
	@Resource
	private IPortalService portalService;
	@Resource
	private LdapConfiguration ldapConfiguration;


	/**
	 * 登录
	 *
	 * @param dto 用户登录请求
	 * @return 用户id
	 */
	@PostMapping("login")
	public SecRestResponse<String> login(@RequestBody PortalUserLoginDTO dto) {
		//NOTE 1-参数校验
		//校验请求中是否携带了门户唯一标志
		//从cookie中获取门户唯一标志
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		if (ArrayUtils.isEmpty(request.getCookies())) {
			throw new BusinessException(ErrorCodeConst.LOGIN_PORTAL_CODE_NOT_EXIST);
		}
		//当前请求的门户portalCode
		Optional<Cookie> cookie = Arrays.stream(request.getCookies()).filter(item -> cookieName.equals(item.getName())).findFirst();
		if (cookie.isEmpty()) {
			throw new BusinessException(ErrorCodeConst.LOGIN_PORTAL_CODE_NOT_EXIST);
		}

		//查询门户信息
		PortalSiteInfoVo portalInfo = portalService.getPortalSiteInfo(cookie.get().getValue());
		if (portalInfo == null) {
			//未获取到门户信息
			throw new BusinessException(ErrorCodeConst.LOGIN_PORTAL_CODE_NOT_EXIST);
		}

		//NOTE 2-LDAP认证
		//base64解码
		dto.setUserPwd(new String(Base64.getDecoder().decode(dto.getUserPwd())));
		boolean authenticate = ldapService.authenticate(dto.getUserName(), dto.getUserPwd());
		if (!authenticate) {
			//认证失败
			throw new BusinessException(ErrorCodeConst.LOGIN_LDAP_AUTH_ERROR);
		}
		//查询用户LDAP信息
		LdapUser ldapUser = ldapService.getLdapUser(dto.getUserName());
		//查询用户所属组织列表
		List<String> orgNameList = ldapUser.getOrgNameList();
		if (CollUtil.isEmpty(orgNameList)) {
			//未找到用户组织
			log.error("未找到用户组织[ldapUser={}]", JSON.toJSONString(ldapUser));
			throw new BusinessException(ErrorCodeConst.LOGIN_LDAP_ORG_NOT_EXIST);
		}

		//NOTE 3-用户信息处理
		PortalUserVO user = portalUserService.getByUserName(dto.getUserName(), null);
		Long userId = null;
		if (user == null) {
			//NOTE 3.1-用户不存在，先创建用户
			//创建用户
			PortalUserAddDTO userAddDTO = new PortalUserAddDTO();
			// 用户名
			userAddDTO.setUserName(dto.getUserName());
			// 门户ID，建立用户和门户的关联：用户 -> 组织 -> 门户
			{
				//FIXME 目前的逻辑是“子组织的用户只能访问直属门户”，后续可扩展为“子组织的门户能访问父组织的门户”
				//获取用户所属组织关联的门户
				String portalCode = null;
				for (String orgName : orgNameList) {
					//直到找到对应的组织
					portalCode = ldapConfiguration.getPortalCode(orgName);
					if(portalCode != null){
						break;
					}
				}
				if(portalCode == null){
					//未获取到组织对应的门户，默认注册到根门户
					log.error("未获取到组织对应的门户，默认注册到根门户[ldapUser={}]", JSON.toJSONString(ldapUser));
					portalCode = rootPortalCode;
				}
				//用户所属组织关联的门户 != 当前请求的门户
				if (!cookie.get().getValue().equals(portalCode)) {
					PortalSiteInfoVo portal = portalService.getPortalSiteInfo(portalCode);
					if(portal == null){
						//未获取到门户信息
						throw new BusinessException(ErrorCodeConst.LOGIN_PORTAL_CODE_NOT_EXIST);
					}
					//错误信息
					String message = String.format(SpringUtil.getBean(MessageSource.class)
							.getMessage(ErrorCodeConst.LOGIN_LDAP_PORTAL_NOT_MATCH, null, LocaleContextHolder.getLocale()), orgNameList.get(0), portal.getPortalName());
					//门户地址
					String redirectUrl = String.format(portalRedirectUrl, portalCode);
					return SecRestResponse.error(ErrorCodeConst.LOGIN_LDAP_PORTAL_NOT_MATCH, message, redirectUrl);
				}
				userAddDTO.setPortalId(portalInfo.getId());
			}
			// 用户来源**1-系统用户 2-LDAP用户**
			userAddDTO.setSourceType(2);
			// 用户别名
			userAddDTO.setAliasName(ldapUser.getUserName());
			log.info("LDAP用户注册[user={}]", JSON.toJSONString(userAddDTO));
			// 用户口令：对明文密码做SM3后保存
			userAddDTO.setUserPwd(SM3Util.digestWithHex(dto.getUserPwd()).toLowerCase());
			userId = portalUserService.add(userAddDTO);
		} else {
			//NOTE 3.1-用户存在，校验用户是否有权登录该门户
			if (user.getSourceType() != 2) {
				//该用户不是LDAP用户，无法使用LDAP登录
				log.error("该用户不是LDAP用户，无法使用LDAP登录[user={}]", JSON.toJSONString(user));
				throw new BusinessException(ErrorCodeConst.LOGIN_LDAP_USER_NOT_LDAP);
			}
			if (user.getPortalId() == null) {
				//用户未绑定门户
				log.error("用户未绑定门户[user={}]", JSON.toJSONString(user));
				throw new BusinessException(ErrorCodeConst.USER_NOT_BIND_PORTAL);
			}
			if (user.getPortalId().longValue() != portalInfo.getId()) {
				//NOTE 用户所属门户 != 当前登录的门户
				//获取用户所属门户
				PortalSiteInfoVo portal = portalService.getPortalSiteInfo(user.getPortalId());
				if(portal == null){
					//未获取到门户信息
					throw new BusinessException(ErrorCodeConst.LOGIN_PORTAL_CODE_NOT_EXIST);
				}
				//错误信息
				String message = String.format(SpringUtil.getBean(MessageSource.class)
						.getMessage(ErrorCodeConst.LOGIN_LDAP_PORTAL_NOT_MATCH, null, LocaleContextHolder.getLocale()), portal.getPortalName());
				//门户地址
				String redirectUrl = String.format(portalRedirectUrl, portal.getPortalCode());
				return SecRestResponse.error(ErrorCodeConst.LOGIN_LDAP_PORTAL_NOT_MATCH, message, redirectUrl);
			}
			userId = user.getId();
		}

		//NOTE 4-生成token
		String token = portalUserService.thirdLogin(userId, UserTypeEnum.PORTAL_USER, cookie.get().getValue());
		return SecRestResponse.success(token);
	}
}