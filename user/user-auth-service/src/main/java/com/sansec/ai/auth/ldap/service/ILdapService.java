package com.sansec.ai.auth.ldap.service;

import com.sansec.ai.auth.ldap.entity.LdapUser;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface ILdapService {
	/**
	 * LDAP用户认证
	 */
	boolean authenticate(String loginName, String password);

	/**
	 * 获取LDAP用户信息
	 *
	 * @param loginName
	 * @return
	 */
	LdapUser getLdapUser(String loginName);

	/**
	 * 查询所有
	 *
	 * @return
	 */
	List<LdapUser> getAll();
}