<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.sansec</groupId>
		<artifactId>sansec-parent</artifactId>
		<version>2.0.3-SNAPSHOT</version>
	</parent>

	<groupId>com.sansec.ai</groupId>
	<artifactId>ai-unified-portal</artifactId>
	<version>1.0-SNAPSHOT</version>
	<packaging>pom</packaging>

	<properties>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

		<ai-unified-portal.version>1.0-SNAPSHOT</ai-unified-portal.version>
		<javax.servlet-api.version>3.1.0</javax.servlet-api.version>
		<commons-exec.version>1.3</commons-exec.version>
		<postgresql.version>42.7.2</postgresql.version>
	</properties>

	<modules>
		<module>common/common-base</module>
<!--		<module>common/common-task-api</module>-->
<!--		<module>common/common-task</module>-->
		<module>common/common-config</module>
		<module>portal/portal-management-api</module>
		<module>portal/portal-management-service</module>
		<module>extra/extra-dify-service</module>
		<module>runner/runner-portal</module>
		<module>runner/runner-gateway</module>
		<module>gateway/ai-gateway</module>
        <module>user/user-manager-service</module>
		<module>user/user-manager-api</module>
		<module>user/user-auth-service</module>
		<module>app/app-management-service</module>
	</modules>

	<dependencyManagement>
		<dependencies>
			<!--二方依赖 Start-->
			<dependency>
				<groupId>com.sansec.ai</groupId>
				<artifactId>common-base</artifactId>
				<version>${ai-unified-portal.version}</version>
			</dependency>
<!--			<dependency>-->
<!--				<groupId>com.sansec.ai</groupId>-->
<!--				<artifactId>common-task-api</artifactId>-->
<!--				<version>${ai-unified-portal.version}</version>-->
<!--			</dependency>-->
<!--			<dependency>-->
<!--				<groupId>com.sansec.ai</groupId>-->
<!--				<artifactId>common-task</artifactId>-->
<!--				<version>${ai-unified-portal.version}</version>-->
<!--			</dependency>-->
			<dependency>
				<groupId>com.sansec.ai</groupId>
				<artifactId>common-config</artifactId>
				<version>${ai-unified-portal.version}</version>
			</dependency>
			<dependency>
				<groupId>com.sansec.ai</groupId>
				<artifactId>extra-dify-service</artifactId>
				<version>${ai-unified-portal.version}</version>
			</dependency>
			<dependency>
				<groupId>com.sansec.ai</groupId>
				<artifactId>portal-management-api</artifactId>
				<version>${ai-unified-portal.version}</version>
			</dependency>
			<dependency>
				<groupId>com.sansec.ai</groupId>
				<artifactId>portal-management-service</artifactId>
				<version>${ai-unified-portal.version}</version>
			</dependency>
			<dependency>
				<groupId>com.sansec.ai</groupId>
				<artifactId>ai-gateway</artifactId>
				<version>${ai-unified-portal.version}</version>
			</dependency>
			<dependency>
				<groupId>com.sansec.ai</groupId>
				<artifactId>user-manager-api</artifactId>
				<version>${ai-unified-portal.version}</version>
			</dependency>
			<dependency>
				<groupId>com.sansec.ai</groupId>
				<artifactId>user-manager-service</artifactId>
				<version>${ai-unified-portal.version}</version>
			</dependency>
			<dependency>
				<groupId>com.sansec.ai</groupId>
				<artifactId>user-auth-service</artifactId>
				<version>${ai-unified-portal.version}</version>
			</dependency>
			<dependency>
				<groupId>com.sansec.ai</groupId>
				<artifactId>app-management-service</artifactId>
				<version>${ai-unified-portal.version}</version>
			</dependency>
			<!--二方依赖 End-->
			<!--三方依赖 Start-->
			<dependency>
				<groupId>javax.servlet</groupId>
				<artifactId>javax.servlet-api</artifactId>
				<version>${javax.servlet-api.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-exec</artifactId>
				<version>${commons-exec.version}</version>
			</dependency>
			<dependency>
				<groupId>org.postgresql</groupId>
				<artifactId>postgresql</artifactId>
				<version>${postgresql.version}</version>
			</dependency>
			<!--三方依赖 End-->
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>17</source>
					<target>17</target>
					<compilerArgs>
						<arg>-parameters</arg>
					</compilerArgs>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<!-- 项目发布 -->
	<distributionManagement>
		<repository>
			<id>sansec-release</id>
			<name>sansec-release</name>
			<url>http://**********:8081/nexus/content/repositories/releases</url>
		</repository>
		<snapshotRepository>
			<id>sansec-snapshots</id>
			<name>sansec-snapshots</name>
			<url>http://**********:8081/nexus/content/repositories/snapshots</url>
		</snapshotRepository>
	</distributionManagement>

	<repositories>
		<!--快照仓库-->
		<repository>
			<id>sansec-snapshots</id>
			<name>sansec-snapshots</name>
			<url>http://**********:8081/nexus/content/repositories/snapshots</url>
			<snapshots>
				<enabled>true</enabled>
				<!--每次compile重新拉取快照版本-->
				<updatePolicy>always</updatePolicy>
			</snapshots>
		</repository>
	</repositories>

	<profiles>
		<profile>
			<id>sonar</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<sonar.jdbc.username>admin</sonar.jdbc.username>
				<sonar.jdbc.password>sansec@2021</sonar.jdbc.password>
				<sonar.host.url>http://**********:9000</sonar.host.url>
				<sonar.projectKey>ai-unified-portal</sonar.projectKey>
				<sonar.projectName>ai-unified-portal</sonar.projectName>
			</properties>
		</profile>
	</profiles>

</project>