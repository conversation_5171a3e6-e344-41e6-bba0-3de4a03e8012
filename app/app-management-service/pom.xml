<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.sansec.ai</groupId>
		<artifactId>ai-unified-portal</artifactId>
		<version>1.0-SNAPSHOT</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>

    <artifactId>app-management-service</artifactId>
	<description>应用管理</description>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

	<dependencies>
		<dependency>
			<groupId>com.sansec.ai</groupId>
			<artifactId>common-base</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sansec.ai</groupId>
			<artifactId>common-config</artifactId>
		</dependency>
        <dependency>
            <groupId>com.sansec.ai</groupId>
            <artifactId>portal-management-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sansec.ai</groupId>
            <artifactId>extra-dify-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sansec.ai</groupId>
            <artifactId>user-manager-api</artifactId>
        </dependency>
    </dependencies>

</project>