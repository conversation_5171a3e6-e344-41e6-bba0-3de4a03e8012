package com.sansec.ai.app.service;

import com.alibaba.fastjson.JSONObject;
import com.sansec.ai.app.request.*;
import com.sansec.ai.extra.dify.entity.AppEntity;
import com.sansec.ai.extra.dify.entity.ConversationEntity;
import com.sansec.ai.portal.entity.vo.PortalSiteInfoVo;
import com.sansec.common.param.response.SecPageVO;

import java.util.List;

public interface AppService {


    SecPageVO<AppEntity> getAppList(AppListDTO appListDTO);

    JSONObject getAppDetail(AppDetailDTO appDetailDTO);

    String getIframeUrl(IframeUrlDTO iframeUrlDTO);

    List<ConversationEntity> getConvserationList(ConversationDTO conversationDTO);

    void removeConversation(DelConversationDTO delConversationDTO);

    void renameConversation(RenameConversationDTO renameConversationDTO);

    PortalSiteInfoVo getSiteInfo();

}
