package com.sansec.ai.app.controller;

import com.sansec.ai.app.service.AppService;
import com.sansec.ai.portal.entity.vo.PortalSiteInfoVo;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("/nahub/app")
@Validated
public class AppNoAuthController {

    @Autowired
    private AppService appService;

    /**
     * 获取门户网站配置
     * 获取指定门户的网站配置信息
     *
     * @return 门户网站配置信息
     */
    @PostMapping("/getSiteInfo")
    public SecRestResponse<PortalSiteInfoVo> getSiteInfo() {
        return ResultUtil.ok(appService.getSiteInfo());
    }
}
