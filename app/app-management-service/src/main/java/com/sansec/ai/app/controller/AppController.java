package com.sansec.ai.app.controller;

import com.sansec.ai.app.request.*;
import com.sansec.ai.app.service.AppService;
import com.sansec.ai.extra.dify.entity.AppEntity;
import com.sansec.ai.extra.dify.entity.ConversationEntity;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/hub/app")
@Validated
public class AppController {

    @Autowired
    private AppService appService;

    @PostMapping("/getAppList")
    public SecRestResponse<SecPageVO<AppEntity>> getAppList(@Validated @RequestBody AppListDTO appListDTO) {
        return ResultUtil.ok(appService.getAppList(appListDTO));
    }

    @PostMapping("/getAppDetail")
    public SecRestResponse<Object> getAppDetail(@Validated @RequestBody AppDetailDTO appDetailDTO) {
        return ResultUtil.ok(appService.getAppDetail(appDetailDTO));
    }

    @PostMapping("/getIframeUrl")
    public SecRestResponse<String> getIframeUrl(@Validated @RequestBody IframeUrlDTO iframeUrlDTO) {
        return ResultUtil.ok(appService.getIframeUrl(iframeUrlDTO));
    }

    @PostMapping("/getConvserationList")
    public SecRestResponse<List<ConversationEntity>> getConvserationList(@Validated @RequestBody ConversationDTO conversationDTO) {
        return ResultUtil.ok(appService.getConvserationList(conversationDTO));
    }

    @PostMapping("/deleteConversation")
    public SecRestResponse<Object> removeConversation(@Validated @RequestBody DelConversationDTO delConversationDTO) {
        appService.removeConversation(delConversationDTO);
        return ResultUtil.ok();
    }

    @PostMapping("/renameConversation")
    public SecRestResponse<Object> renameConversation(@Validated @RequestBody RenameConversationDTO renameConversationDTO) {
        appService.renameConversation(renameConversationDTO);
        return ResultUtil.ok();
    }
}
