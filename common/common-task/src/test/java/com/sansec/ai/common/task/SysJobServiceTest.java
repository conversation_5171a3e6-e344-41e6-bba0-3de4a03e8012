package com.sansec.ai.common.task;

import com.sansec.common.exception.BusinessException;
import com.sansec.ai.common.constant.TaskConcurrentConst;
import com.sansec.ai.common.constant.TaskMisfireEnum;
import com.sansec.ai.common.quartz.entity.request.JobCreateRequest;
import com.sansec.ai.common.quartz.service.SysJobService;
import com.sansec.ai.common.utils.CronUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.AntPathMatcher;

/**
 * <AUTHOR>
 * @since 2025/2/28 11:13
 */
@Slf4j
@Transactional
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE, classes = {AntPathMatcher.class})
public class SysJobServiceTest {

	@Autowired
	private SysJobService sysJobService;

	/**
	 * 动态添加定时任务
	 */
//	@Test
	void add() {
		String cron = "0 0/1 * * * ?";
		//1.校验cron表达式是否正确
		if (!CronUtils.isValid(cron)) {
			return;
		}
		//2.校验cron表达式是否大于等于允许的最小秒数
		CronUtils.isCronGreaterThanSecondInterval(cron, 60);

		//3.创建定时任务
		JobCreateRequest request = new JobCreateRequest();
		//单元测试任务名
		request.setJobName("巡检策略名称");
		//单元测试任务组
		request.setJobGroup("insp-policy");
		//执行周期
		request.setCronExpression(cron);
		//是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）
		request.setConcurrent(TaskConcurrentConst.CONCURRENT_YES);
		//计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）
		request.setMisfirePolicy(TaskMisfireEnum.DO_NOTHING.value);
		//设置定时任务执行的方法
		request.setJsonParam(String.format("sysJobLogService.clearLog(%s)", 10));
		try {
			Long jobId = sysJobService.add(request);
		} catch (BusinessException e) {
			Assertions.assertEquals(0, 1, "新增任务异常");
		}
	}

}
