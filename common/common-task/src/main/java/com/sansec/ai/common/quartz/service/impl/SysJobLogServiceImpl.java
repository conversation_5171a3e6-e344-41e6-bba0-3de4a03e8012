package com.sansec.ai.common.quartz.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ai.common.quartz.mapper.SysJobLogMapper;
import com.sansec.ai.common.quartz.entity.po.SysJobLogPO;
import com.sansec.ai.common.quartz.service.SysJobLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.ZoneOffset;

/**
 * 定时任务执行日志表;(SYS_JOB_LOG)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-2-24
 */
@Service
@Slf4j
@Validated
public class SysJobLogServiceImpl extends ServiceImpl<SysJobLogMapper, SysJobLogPO> implements SysJobLogService {

	/**
	 * 默认保留最近X天的日志，X=3
	 */
	@Value("${corn.job.clear.days:3}")
	private int defaultDaysBefore;

	/**
	 * 每天凌晨1点执行一次，清理日志表数据，报保留最近三天的日志
	 */
	@Scheduled(cron = "${cron.job.clear:1 1 1 * * ?}")
	public void clear() {
		try {
			this.clearLog(defaultDaysBefore);
		} catch (Exception e) {
			log.error("quartz job log clear error", e);
		}
	}

	@Override
	public void clearLog(int daysBefore) {
		LocalDate now = LocalDate.now();
		LocalDate time = now.minusDays(daysBefore);
		Long millTime = time.atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
		baseMapper.delete(Wrappers.<SysJobLogPO>lambdaQuery().lt(SysJobLogPO::getTriggerTime, millTime));
	}

}