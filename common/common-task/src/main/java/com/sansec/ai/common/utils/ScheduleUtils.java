package com.sansec.ai.common.utils;

import com.sansec.ai.common.quartz.constant.SysJobConst;
import com.sansec.ai.common.quartz.handler.QuartzDisallowConcurrentExecution;
import com.sansec.ai.common.quartz.handler.QuartzJobExecution;
import com.sansec.common.exception.BusinessException;
import com.sansec.ai.common.constant.TaskMisfireEnum;
import com.sansec.ai.common.quartz.entity.po.SysJobPO;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;

/**
 * 定时任务工具类
 *
 * <AUTHOR>
 * @since 2025-2-24
 **/
public class ScheduleUtils {
	private ScheduleUtils() {
		throw new IllegalStateException("Utility class");
	}

	/**
	 * 得到quartz任务类
	 *
	 * @param sysJob 执行计划
	 * @return 具体执行任务类
	 */
	private static Class<? extends Job> getQuartzJobClass(SysJobPO sysJob) {
		boolean isConcurrent = "0".equals(sysJob.getConcurrent());
		return isConcurrent ? QuartzJobExecution.class : QuartzDisallowConcurrentExecution.class;
	}

	/**
	 * 构建任务触发对象
	 *
	 * @param jobId
	 * @param jobGroup
	 * @return
	 */
	public static TriggerKey getTriggerKey(Long jobId, String jobGroup) {
		return TriggerKey.triggerKey(SysJobConst.TASK_CLASS_NAME + jobId, jobGroup);
	}

	/**
	 * 构建任务键对象
	 *
	 * @param jobId
	 * @param jobGroup
	 * @return
	 */
	public static JobKey getJobKey(Long jobId, String jobGroup) {
		return JobKey.jobKey(SysJobConst.TASK_CLASS_NAME + jobId, jobGroup);
	}

	/**
	 * 创建定时任务
	 *
	 * @param scheduler
	 * @param job
	 * @throws SchedulerException
	 */
	public static void createScheduleJob(Scheduler scheduler, SysJobPO job) throws SchedulerException {
		Class<? extends Job> jobClass = getQuartzJobClass(job);
		// 构建job信息
		Long jobId = job.getJobId();
		String jobGroup = job.getJobGroup();
		JobDetail jobDetail = JobBuilder.newJob(jobClass).withIdentity(getJobKey(jobId, jobGroup)).build();

		// 表达式调度构建器
		CronScheduleBuilder cronScheduleBuilder = CronScheduleBuilder.cronSchedule(job.getCronExpression());
		cronScheduleBuilder = handleCronScheduleMisfirePolicy(job, cronScheduleBuilder);

		// 按新的cronExpression表达式构建一个新的trigger
		CronTrigger trigger = TriggerBuilder.newTrigger().withIdentity(getTriggerKey(jobId, jobGroup))
				.withSchedule(cronScheduleBuilder).build();

		// 放入参数，运行时的方法可以获取
		jobDetail.getJobDataMap().put(SysJobConst.TASK_PROPERTIES, job);

		// 判断是否存在
		if (scheduler.checkExists(getJobKey(jobId, jobGroup))) {
			// 防止创建时存在数据问题 先移除，然后在执行创建操作
			scheduler.deleteJob(getJobKey(jobId, jobGroup));
		}

		scheduler.scheduleJob(jobDetail, trigger);

		// 暂停任务
		if (job.getJobStatus().equals(SysJobConst.Status.PAUSE.getValue())) {
			scheduler.pauseJob(ScheduleUtils.getJobKey(jobId, jobGroup));
		}
	}

	/**
	 * 设置定时任务策略
	 *
	 * @param job
	 * @param cb
	 * @return
	 */
	public static CronScheduleBuilder handleCronScheduleMisfirePolicy(SysJobPO job, CronScheduleBuilder cb) {
		if (StringUtils.isEmpty(job.getMisfirePolicy())) {
			return cb.withMisfireHandlingInstructionDoNothing();
		}
		switch (TaskMisfireEnum.getEnum(job.getMisfirePolicy())) {
			case DEFAULT:
				return cb;
			case IGNORE_MISFIRES:
				return cb.withMisfireHandlingInstructionIgnoreMisfires();
			case FIRE_AND_PROCEED:
				return cb.withMisfireHandlingInstructionFireAndProceed();
			case DO_NOTHING:
				return cb.withMisfireHandlingInstructionDoNothing();
			default:
				throw new BusinessException("The task misfire policy '" + job.getMisfirePolicy()
						+ "' cannot be used in cron schedule tasks");
		}
	}

	/**
	 * 检查包名是否为白名单配置
	 *
	 * @param invokeTarget 目标字符串
	 * @return 结果
	 */
	public static boolean whiteList(String invokeTarget) {
		String packageName = StringUtils.substringBefore(invokeTarget, "(");
		int count = StringUtils.countMatches(packageName, ".");
		if (count > 1) {
			return StringUtils.containsAnyIgnoreCase(invokeTarget, SysJobConst.JOB_WHITELIST_STR);
		}
		return true;
	}
}