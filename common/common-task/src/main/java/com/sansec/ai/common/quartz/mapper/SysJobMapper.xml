<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sansec.ai.common.quartz.mapper.SysJobMapper">
	<sql id="Base_Column_List">
		JOB_ID
		,JOB_NAME,JOB_GROUP,SERVER_ID,METHOD_URL,JSON_PARAM,CRON_EXPRESSION,MISFIRE_POLICY,CONCURRENT,JOB_STATUS,CREATED_BY,CREATE_TIME,UPDATED_BY,UPDATE_TIME,REMARK
	</sql>

</mapper>