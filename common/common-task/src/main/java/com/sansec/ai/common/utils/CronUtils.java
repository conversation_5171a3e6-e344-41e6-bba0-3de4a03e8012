package com.sansec.ai.common.utils;

import cn.hutool.core.date.DateUtil;
import lombok.SneakyThrows;
import org.quartz.CronExpression;
import org.quartz.TriggerUtils;
import org.quartz.impl.triggers.CronTriggerImpl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @Describe cron表达式工具类
 * @Author:  wwl
 * @Date 2023/3/4
 **/
public class CronUtils {
    private CronUtils() {
        throw new IllegalStateException("Utility class");
    }

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    /**
     * 返回一个布尔值代表一个给定的Cron表达式的有效性
     *
     * @param cronExpression Cron表达式
     * @return boolean 表达式是否有效
     */
    public static boolean isValid(String cronExpression) {
        return CronExpression.isValidExpression(cronExpression);
    }

	/**
	 * 判断cron表达式周期是否大于最小需求的秒数
	 * @param cronExpression cron表达式
	 * @param minIntervalSecond 最小秒数，比如300
	 * @return 满足要求-true
	 */
	@SneakyThrows
	public static boolean isCronGreaterThanSecondInterval(String cronExpression, long minIntervalSecond) {
		CronExpression cron = new CronExpression(cronExpression);
		Date nextExecuteTime = cron.getNextValidTimeAfter(new Date());
		LocalDateTime targetExecuteTime = DateUtil.toLocalDateTime(cron.getNextValidTimeAfter(nextExecuteTime));

		LocalDateTime minMinutesLater = DateUtil.toLocalDateTime((nextExecuteTime)).plusSeconds(minIntervalSecond);

		// 检查下一个执行时间是否在最小时间之后
		return targetExecuteTime.isAfter(minMinutesLater) || targetExecuteTime.equals(minMinutesLater);
	}

    /**
     * 返回一个字符串值,表示该消息无效Cron表达式给出有效性
     *
     * @param cronExpression Cron表达式
     * @return String 无效时返回表达式错误描述,如果有效返回null
     */
    public static String getInvalidMessage(String cronExpression) {
        try {
            new CronExpression(cronExpression);
            return null;
        } catch (ParseException pe) {
            return pe.getMessage();
        }
    }

    /**
     * 返回下一个执行时间根据给定的Cron表达式
     *
     * @param cronExpression Cron表达式
     * @return Date 下次Cron表达式执行时间
     */
    public static Date getNextExecution(String cronExpression) {
        try {
            CronExpression cron = new CronExpression(cronExpression);
            return cron.getNextValidTimeAfter(new Date(System.currentTimeMillis()));
        } catch (ParseException e) {
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    /**
     * 通过表达式获取近10次的执行时间
     *
     * @param cron 表达式
     * @return 时间列表
     */
    public static List<String> getRecentTriggerTime(String cron) {
        List<String> list = new ArrayList<>();
        try {
            CronTriggerImpl cronTriggerImpl = new CronTriggerImpl();
            cronTriggerImpl.setCronExpression(cron);
            List<Date> dates = TriggerUtils.computeFireTimes(cronTriggerImpl, null, 10);
            for (Date date : dates) {
                list.add(parseDateToStr(YYYY_MM_DD_HH_MM_SS, date));
            }
        } catch (ParseException e) {
            return Collections.emptyList();
        }
        return list;
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }
}
