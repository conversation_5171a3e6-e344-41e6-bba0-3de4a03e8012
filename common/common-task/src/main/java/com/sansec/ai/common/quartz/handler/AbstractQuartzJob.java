package com.sansec.ai.common.quartz.handler;

import com.sansec.ai.common.quartz.constant.SysJobConst;
import com.sansec.ai.common.quartz.mapper.SysJobLogMapper;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.utils.DateUtils;
import com.sansec.ai.common.quartz.entity.po.SysJobLogPO;
import com.sansec.ai.common.quartz.entity.po.SysJobPO;
import com.sansec.ai.common.utils.InetAddressUtil;
import com.sansec.ai.common.utils.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.InvocationTargetException;
import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 抽象quartz调用
 *
 * <AUTHOR>
 * @since 2025-2-24
 */
@Slf4j
public abstract class AbstractQuartzJob implements Job {
	private static final String LOCK_PRX_NAME = "ccsp:quartz:%s_%s";
	/**
	 * 线程本地变量
	 */
	private static ThreadLocal<LocalDateTime> threadLocal = new ThreadLocal<>();

	@Override
	public void execute(JobExecutionContext context) {
		SysJobPO sysJob = new SysJobPO();
		BeanUtils.copyProperties(context.getMergedJobDataMap().get(SysJobConst.TASK_PROPERTIES), sysJob);
		this.setTraceIdLog(sysJob.getJobId());
		//FIXME 集群环境下，需要对定时任务加锁
//		String lock = String.format(LOCK_PRX_NAME, sysJob.getJobGroup(), sysJob.getJobName());
//		DistributedLock distributedLock = SpringBeanUtils.getBean(DistributedLock.class);
		try {
//			boolean isLock = distributedLock.tryLock(lock, TimeUnit.SECONDS, 1, 5);
//			if (!isLock) {
//				log.warn("任务未在本机执行[{}]", lock);
//				return;
//			}
			before(context, sysJob);
			doExecute(context, sysJob);
			after(context, sysJob, null);
//		} catch (RedisException e) {
//			log.warn("任务未在本机执行[{}]", lock, e);
		} catch (Exception e) {
			log.error("任务执行异常  - ：", e);
			after(context, sysJob, e);
		} finally {
			try {
				//防止定时任务执行时间小于1秒导致重复执行
				Thread.sleep(5000);
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
			}
//			distributedLock.unlock(lock);
		}
	}

	/**
	 * 设置日志id
	 *
	 * @param id 任务id
	 */
	private void setTraceIdLog(Long id) {
		MDC.clear();
		MDC.put("X-B3-Parent-SpanName", "JOB");
		MDC.put("X-B3-TraceId", String.valueOf(id));
	}

	/**
	 * 执行前
	 *
	 * @param context 工作执行上下文对象
	 * @param sysJob  系统计划任务
	 */
	protected void before(JobExecutionContext context, SysJobPO sysJob) {
		LocalDateTime now = LocalDateTime.now();
		threadLocal.set(now);
		// final SysJobLogPO sysJobLogPO = new SysJobLogPO();
		// sysJobLogPO.setJobId(sysJob.getJobId());
		// sysJobLogPO.setJobLogId(IdGenerator.ins().generator());
		// sysJobLogPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
		// sysJobLogPO.setTriggerTime(System.currentTimeMillis());
		// sysJobLogPO.setStatus(ScheduleConstants.SUCCESS);
		// sysJobLogPO.setJobMessage("任务组=[" + sysJob.getJobGroup() + "]任务名=[" + sysJob.getJobName() + "]启动");
		// SpringUtils.getBean(SysJobLogMapper.class).insert(sysJobLogPO);
	}

	/**
	 * 执行后
	 *
	 * @param context 工作执行上下文对象
	 * @param sysJob  系统计划任务
	 */
	protected void after(JobExecutionContext context, SysJobPO sysJob, Exception e) {
		LocalDateTime startTime = threadLocal.get();
		threadLocal.remove();
		final SysJobLogPO sysJobLogPO = new SysJobLogPO();
		sysJobLogPO.setJobId(sysJob.getJobId());
		sysJobLogPO.setJobLogId(IdGenerator.ins().generator());
		sysJobLogPO.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
		sysJobLogPO.setTriggerTime(System.currentTimeMillis());
		Duration duration = Duration.between(startTime, LocalDateTime.now());
		long runMs = duration.toMillis();
		sysJobLogPO.setJobMessage("host=[" + InetAddressUtil.getHostIp() + "] 任务组=[" + sysJob.getJobGroup() + "]任务名=[" + sysJob.getJobName() + "]方法=[" + sysJob.getJsonParam() + "]总共耗时=[" + runMs + "]毫秒");
		if (e != null) {
			sysJobLogPO.setStatus(SysJobConst.FAIL);
			StringWriter sw = new StringWriter();
			e.printStackTrace(new PrintWriter(sw, true));
			String errorMsg = StringUtils.substring(sw.toString(), 0, 2000);
			sysJobLogPO.setExceptionInfo(errorMsg);
		} else {
			sysJobLogPO.setStatus(SysJobConst.SUCCESS);
		}
		SpringBeanUtils.getBean(SysJobLogMapper.class).insert(sysJobLogPO);
	}

	/**
	 * 执行方法，由子类重载
	 *
	 * @param context 工作执行上下文对象
	 * @param sysJob  系统计划任务
	 * @throws ClassNotFoundException
	 * @throws InvocationTargetException
	 * @throws NoSuchMethodException
	 * @throws IllegalAccessException
	 * @throws InstantiationException
	 */
	protected abstract void doExecute(JobExecutionContext context, SysJobPO sysJob) throws ClassNotFoundException, InvocationTargetException, NoSuchMethodException, IllegalAccessException, InstantiationException;


}
