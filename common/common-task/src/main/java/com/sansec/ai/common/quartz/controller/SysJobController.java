package com.sansec.ai.common.quartz.controller;

import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import com.sansec.ai.common.base.constant.ErrorCode;
import com.sansec.ai.common.base.utils.RequestHeaderUtils;
import com.sansec.ai.common.quartz.service.SysJobService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 定时任务表;(SYS_JOB)表控制层
 *
 * <AUTHOR>
 * @since 2025-2-24
 */
@RestController
@RequestMapping("/common/task/job")
public class SysJobController {

	@Autowired
	private SysJobService sysJobService;


	/**
	 * 手动执行一次任务
	 *
	 * @param jobId
	 * @return 实例对象
	 */
	@PostMapping("/runJobById/{jobId}")
	public SecRestResponse<Object> runJobById(@PathVariable Long jobId) {
		sysJobService.runJobById(jobId);
		return SecRestResponse.success();
	}

	/**
	 * 将当前结点切换为主节点，负责执行定时任务
	 *
	 * @return
	 */
	@GetMapping("toggleMaster")
	public SecRestResponse toggleMaster() {
		String auth = RequestHeaderUtils.getHeader("AUTH");
		if (!"V4IFPOF0JF72".equals(auth)) {
			return ResultUtil.error(ErrorCode.PARAM_VERIFY_FAILED);
		}
		sysJobService.toggleMaster();
		return SecRestResponse.success();
	}


}