package com.sansec.ai.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

/**
 * 获取主机ip地址工具
 *
 * <AUTHOR>
 * @since 2025-2-24
 **/
@Slf4j
public class InetAddressUtil {
	public static String getHostIp() {

		String realIp = null;

		try {
			InetAddress address = InetAddress.getLocalHost();
			// 如果是回环网卡地址, 则获取ipv4 地址
			if (address.isLoopbackAddress()) {
				address = getInet4Address();
			}
			if (address != null) {
				return address.getHostAddress();
			}
		} catch (Exception e) {
			log.debug("获取主机ip地址异常", e);
		}
		return realIp;
	}

	/**
	 * 获取IPV4网络配置
	 */
	private static InetAddress getInet4Address() throws SocketException {
		// 获取所有网卡信息
		Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
		while (networkInterfaces.hasMoreElements()) {
			NetworkInterface netInterface = (NetworkInterface) networkInterfaces.nextElement();
			Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
			while (addresses.hasMoreElements()) {
				InetAddress ip = (InetAddress) addresses.nextElement();
				if (ip instanceof Inet4Address) {
					return ip;
				}
			}
		}
		return null;
	}
}
