package com.sansec.ai.common.quartz.constant;

/**
 * 任务调度通用常量
 *
 * <AUTHOR>
 * @since 2025-2-24
 **/
public class SysJobConst {

	/**
	 * http请求
	 */
	public static final String HTTP = "http://";

	/**
	 * https请求
	 */
	public static final String HTTPS = "https://";
	public static final String TASK_CLASS_NAME = "TASK_SUPERVISION";

	/**
	 * 执行目标key
	 */
	public static final String TASK_PROPERTIES = "TASK_PROPERTIES";

	/**
	 * 通用成功标识
	 */
	public static final String SUCCESS = "1";

	/**
	 * 通用失败标识
	 */
	public static final String FAIL = "0";
	/**
	 * 定时任务白名单配置（仅允许访问的包名，如其他需要可以自行添加）
	 */
	public static final String[] JOB_WHITELIST_STR = {"com.sansec"};
	/**
	 * 定时任务违规的字符
	 */
	public static final String[] JOB_ERROR_STR = {"java.net.URL", "javax.naming.InitialContext", "org.yaml.snakeyaml",
			"org.springframework", "org.apache"};
	/**
	 * RMI 远程方法调用
	 */
	public static final String LOOKUP_RMI = "rmi:";

	/**
	 * LDAP 远程方法调用
	 */
	public static final String LOOKUP_LDAP = "ldap:";

	/**
	 * LDAPS 远程方法调用
	 */
	public static final String LOOKUP_LDAPS = "ldaps:";

	public enum Status {
		/**
		 * 正常
		 */
		NORMAL("0"),
		/**
		 * 暂停
		 */
		PAUSE("1");

		private String value;

		private Status(String value) {
			this.value = value;
		}

		public String getValue() {
			return value;
		}
	}

}
