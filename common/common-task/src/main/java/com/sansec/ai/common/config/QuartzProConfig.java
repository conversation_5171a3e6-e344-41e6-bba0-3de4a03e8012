package com.sansec.ai.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import java.util.Properties;

/**
 * 定时任务属性配置
 *
 * <AUTHOR>
 * @since 2025-2-24
 **/
@Configuration
public class QuartzProConfig {
	@Bean
	public SchedulerFactoryBean schedulerFactory() {
		SchedulerFactoryBean factory = new SchedulerFactoryBean();
		// 使用内存中的JobStore
		factory.setOverwriteExistingJobs(true);
		factory.setQuartzProperties(quartzProperties());
		return factory;
	}

	@Bean
	public Properties quartzProperties() {
		Properties properties = new Properties();
		properties.put("org.quartz.threadPool.threadCount", "30");
		return properties;
	}
}