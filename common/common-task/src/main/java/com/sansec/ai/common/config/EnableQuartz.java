package com.sansec.ai.common.config;

import com.sansec.ai.common.quartz.config.BeanImportSelector;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * 定时任务开启
 *
 * <AUTHOR>
 * @since 2025-2-24
 **/
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
@Import(BeanImportSelector.class)
public @interface EnableQuartz {
	String[] classes() default {"com.sansec.supervision.common.quartz.config.QuartzConfig"};

	/**
	 * 开关  false关闭 true开启
	 *
	 * @return
	 */
	boolean value() default true;
}