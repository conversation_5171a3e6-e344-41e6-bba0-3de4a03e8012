package com.sansec.ai.common.quartz.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ai.common.quartz.constant.SysJobConst;
import com.sansec.ai.common.quartz.convert.SysJobConvert;
import com.sansec.ai.common.quartz.mapper.SysJobMapper;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.utils.DateUtils;
import com.sansec.ai.common.base.constant.ErrorCode;
import com.sansec.ai.common.quartz.entity.po.SysJobPO;
import com.sansec.ai.common.quartz.entity.request.JobCreateRequest;
import com.sansec.ai.common.quartz.service.SysJobService;
import com.sansec.ai.common.utils.CronUtils;
import com.sansec.ai.common.utils.ScheduleUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.quartz.JobDataMap;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 定时任务表;(SYS_JOB)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-2-24
 */
@Service
@Slf4j
@Validated
@EnableScheduling
public class SysJobServiceImpl extends ServiceImpl<SysJobMapper, SysJobPO> implements SysJobService {
	@Autowired
	private Scheduler scheduler;
	@Autowired
	private SysJobConvert convert;
	@Value("${spring.application.name}")
	private String serverId;
	/**
	 * 当前已加载的定时任务列表
	 */
	private List<SysJobPO> currentJobList = new ArrayList<>();

	/**
	 * 项目启动时，初始化定时器
	 * 主要是防止手动修改数据库导致未同步到定时任务处理
	 */
	@Scheduled(initialDelay = 1000, fixedDelay = 60000)
	public void init() {
		try {
			//查询数据库中的定时任务列表
			List<SysJobPO> dbJobList = baseMapper.selectList(Wrappers.<SysJobPO>lambdaQuery()
					.eq(SysJobPO::getJobStatus, SysJobConst.Status.NORMAL.getValue())
					.eq(StringUtils.isNotBlank(serverId), SysJobPO::getServerId, serverId));
			//待新增的任务
			Collection<SysJobPO> addJobList = CollectionUtils.subtract(dbJobList, currentJobList);
			//不变的任务
			Collection<SysJobPO> interJobList = CollectionUtils.intersection(dbJobList, currentJobList);
			//待删除的任务
			Collection<SysJobPO> delJobList = CollectionUtils.subtract(currentJobList, dbJobList);
			if (!addJobList.isEmpty() || !delJobList.isEmpty()) {
				for (SysJobPO job : delJobList) {
					scheduler.deleteJob(ScheduleUtils.getJobKey(job.getJobId(), job.getJobGroup()));
				}
				for (SysJobPO job : addJobList) {
					ScheduleUtils.createScheduleJob(scheduler, job);
				}
				currentJobList = new ArrayList<>();
				currentJobList.addAll(addJobList);
				currentJobList.addAll(interJobList);
			}
		} catch (SchedulerException e) {
			log.error("定时任务动态加载失败", e);
		}
	}

	/**
	 * 删除缓存，并查询新的任务放入
	 *
	 * @param jobId
	 */
	private void updateJobList(Long jobId) {
		currentJobList.removeIf(job -> job.getJobId().equals(jobId));
		SysJobPO sysJobPOAdd = baseMapper.selectById(jobId);
		if (sysJobPOAdd != null && sysJobPOAdd.getJobStatus().equals(SysJobConst.Status.NORMAL.getValue())) {
			currentJobList.add(sysJobPOAdd);
		}
	}


	/**
	 * 将当前结点切换为主节点，负责执行定时任务
	 *
	 * @throws BusinessException
	 */
	@Override
	public void toggleMaster() throws BusinessException {
		//所有定时任务修改为本机执行
		log.info("所有定时任务修改为本机执行");
		baseMapper.update(new LambdaUpdateWrapper<SysJobPO>()
				.set(SysJobPO::getServerId, serverId)
				.set(SysJobPO::getUpdateTime, DateUtils.date2DateTimeString(new Date())));
		new Thread(() -> {
			try {
				//重新加载定时任务
				log.info("重新加载定时任务");
				Thread.sleep(1000L);
				init();
			} catch (Exception e) {
				log.error("重新加载定时任务失败", e);
			}
		}).start();
	}

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	public Long add(JobCreateRequest request) throws BusinessException {
		SysJobPO entity = convert.dtoToPo(request);
		check(entity);
		if (entity.getJobId() == null || entity.getJobId() == 0) {
			entity.setJobId(IdGenerator.ins().generator());
		} else {
			boolean isExists = baseMapper.exists(Wrappers.<SysJobPO>lambdaQuery().eq(SysJobPO::getJobId, entity.getJobId())
					.or().eq(SysJobPO::getJobName, entity.getJobName()).eq(SysJobPO::getJobGroup, entity.getJobGroup()));
			if (isExists) {
				throw new BusinessException(ErrorCode.SYS_JOB_CREATE_FAILED, "定时任务已存在");
			}
		}
		entity.setServerId(serverId);
		entity.setCreateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
		if (StringUtils.isBlank(entity.getJobStatus())) {
			entity.setJobStatus(SysJobConst.Status.NORMAL.getValue());
		}
		int rows = baseMapper.insert(entity);
		if (rows > 0) {
			updateJobList(entity.getJobId());
			try {
				ScheduleUtils.createScheduleJob(scheduler, entity);
			} catch (SchedulerException e) {
				log.error("定时任务调度器添加异常", e);
				throw new BusinessException(ErrorCode.SYS_JOB_CREATE_FAILED, "定时任务调度器添加异常");
			}
		}
		return entity.getJobId();
	}

	/**
	 * 更新数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	public void edit(@Valid @NotNull JobCreateRequest request) throws BusinessException {
		SysJobPO sysJobPO = convert.dtoToPo(request);
		sysJobPO.setUpdateTime(DateUtils.localDateTime2String(LocalDateTime.now()));
		int count = baseMapper.updateById(sysJobPO);
		if (count == 0) {
			return;
		}
		try {
			updateJobList(sysJobPO.getJobId());
			// 判断是否存在
			JobKey jobKey = ScheduleUtils.getJobKey(sysJobPO.getJobId(), sysJobPO.getJobGroup());
			if (scheduler.checkExists(jobKey)) {
				// 防止创建时存在数据问题 先移除，然后在执行创建操作
				scheduler.deleteJob(jobKey);
			}
			ScheduleUtils.createScheduleJob(scheduler, sysJobPO);
		} catch (SchedulerException e) {
			log.error("定时任务调度器更新异常", e);
			throw new BusinessException(ErrorCode.SYS_JOB_UPDATE_FAILED, "定时任务调度器添加异常");
		}
	}

	/**
	 * 通过主键删除数据
	 *
	 * @param jobId
	 * @return 实例对象
	 */
	@Override
	public void deleteById(Long jobId) throws BusinessException {
		SysJobPO sysJobPO = baseMapper.selectById(jobId);
		int rows = baseMapper.deleteById(jobId);
		if (rows > 0) {
			currentJobList.removeIf(job -> job.getJobId().equals(jobId));
			try {
				scheduler.deleteJob(ScheduleUtils.getJobKey(sysJobPO.getJobId(), sysJobPO.getJobGroup()));
			} catch (SchedulerException e) {
				log.error("定时任务调度器删除异常", e);
				throw new BusinessException(ErrorCode.SYS_JOB_DELETE_FAILED, "定时任务调度器删除异常");
			}
		}
	}

	/**
	 * 执行一次任务
	 *
	 * @param jobId
	 */
	@Override
	public void runJobById(@NotNull Long jobId) throws BusinessException {
		SysJobPO sysJobPO = baseMapper.selectById(jobId);
		if (sysJobPO == null) {
			throw new BusinessException("000000", "定时任务不存在");
		}
		JobDataMap dataMap = new JobDataMap();
		dataMap.put(SysJobConst.TASK_PROPERTIES, sysJobPO);
		try {
			scheduler.triggerJob(ScheduleUtils.getJobKey(jobId, sysJobPO.getJobGroup()), dataMap);
		} catch (SchedulerException e) {
			log.error("定时任务调度器任务执行异常,jobId={}", jobId, e);
			throw new BusinessException("000000", "定时任务调度器任务执行异常");
		}
	}

	/**
	 * 校验任务参数
	 *
	 * @param entity
	 */
	private void check(SysJobPO entity) {
		if (!CronUtils.isValid(entity.getCronExpression())) {
			throw new BusinessException(ErrorCode.PARAM_VERIFY_FAILED, "Cron表达式不正确");
		}
		if (org.apache.commons.lang3.StringUtils.containsIgnoreCase(entity.getJsonParam(), SysJobConst.LOOKUP_RMI)) {
			throw new BusinessException(ErrorCode.PARAM_VERIFY_FAILED, "目标字符串不允许'rmi'调用");
		}
		if (org.apache.commons.lang3.StringUtils.containsAnyIgnoreCase(entity.getJsonParam(), SysJobConst.LOOKUP_LDAP, SysJobConst.LOOKUP_LDAPS)) {
			throw new BusinessException(ErrorCode.PARAM_VERIFY_FAILED, "目标字符串不允许'ldap(s)'调用");
		}
		if (org.apache.commons.lang3.StringUtils.containsAnyIgnoreCase(entity.getJsonParam(), SysJobConst.HTTP, SysJobConst.HTTPS)) {
			throw new BusinessException(ErrorCode.PARAM_VERIFY_FAILED, "目标字符串不允许'http(s)'调用");
		}
		if (org.apache.commons.lang3.StringUtils.containsAnyIgnoreCase(entity.getJsonParam(), SysJobConst.JOB_ERROR_STR)) {
			throw new BusinessException(ErrorCode.PARAM_VERIFY_FAILED, "目标字符串存在违规");
		}
		if (!ScheduleUtils.whiteList(entity.getJsonParam())) {
			throw new BusinessException(ErrorCode.PARAM_VERIFY_FAILED, "目标字符串不在白名单内");
		}
	}
}

