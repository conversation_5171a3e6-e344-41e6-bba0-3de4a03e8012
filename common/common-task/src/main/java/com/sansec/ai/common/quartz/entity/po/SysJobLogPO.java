package com.sansec.ai.common.quartz.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.common.param.base.BasePO;
import lombok.Data;

/**
 * 定时任务执行日志表;
 * <AUTHOR>
 * @since 2025-2-24
 */
@TableName("SYS_JOB_LOG")
@Data
public class SysJobLogPO extends BasePO {
    /**
     * 任务日志ID
     */
    @TableId(type = IdType.INPUT)
    private Long jobLogId;
    /**
     * 任务ID
     */
    private Long jobId;
    /**
     * 日志信息
     */
    private String jobMessage;
    /**
     * 执行状态（0正常 1失败）
     */
    private String status;
    /**
     * 异常信息
     */
    private String exceptionInfo;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 触发时间
     */
    private Long triggerTime;

}