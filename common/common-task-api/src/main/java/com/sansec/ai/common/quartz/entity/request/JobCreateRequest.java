package com.sansec.ai.common.quartz.entity.request;

import lombok.Data;

/**
 * 新增定时任务请求
 *
 * <AUTHOR>
 * @since 2025-2-24
 */
@Data
public class JobCreateRequest {
	/**
	 * 任务名称
	 */
	private String jobName;
	/**
	 * 任务组名
	 */
	private String jobGroup;
	/**
	 * json格式参数
	 */
	private String jsonParam;
	/**
	 * CRON执行表达式
	 */
	private String cronExpression;
	/**
	 * 计划执行错误策略;计划执行错误策略（1立即执行 2执行一次 3放弃执行）
	 */
	private String misfirePolicy;
	/**
	 * 是否并发执行（0允许 1禁止）;是否并发执行（0允许 1禁止）
	 */
	private String concurrent;
	/**
	 * 备注
	 */
	private String remark;

}