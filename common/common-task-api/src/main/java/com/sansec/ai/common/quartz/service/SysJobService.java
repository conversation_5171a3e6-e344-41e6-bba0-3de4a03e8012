package com.sansec.ai.common.quartz.service;

import com.sansec.ai.common.quartz.entity.request.JobCreateRequest;
import com.sansec.common.exception.BusinessException;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 定时任务表;(SYS_JOB)表服务接口
 *
 * <AUTHOR>
 * @since 2025-2-24
 */
public interface SysJobService {

	/**
	 * 将当前结点切换为主节点，负责执行定时任务
	 * @throws BusinessException
	 */
	void toggleMaster() throws BusinessException;

	/**
	 * 新增数据
	 *
	 * @param request
	 * @return 定时任务ID
	 * @throws BusinessException
	 */
	Long add(@Valid @NotNull JobCreateRequest request) throws BusinessException;

	/**
	 * 更新数据
	 *
	 * @param request
	 * @return 实例对象
	 */
	void edit(@Valid @NotNull JobCreateRequest request) throws BusinessException;

	/**
	 * 通过主键删除数据
	 *
	 * @param jobId
	 * @return 实例对象
	 */
	void deleteById(@NotNull Long jobId) throws BusinessException;

	/**
	 * 执行一次任务
	 *
	 * @param jobId
	 * @throws BusinessException
	 */
	void runJobById(@NotNull Long jobId) throws BusinessException;

}