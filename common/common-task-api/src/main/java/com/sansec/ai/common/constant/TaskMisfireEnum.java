package com.sansec.ai.common.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * 计划执行错误时采取的策略
 *
 * <AUTHOR>
 * @since 2025/2/28 13:44
 */
public enum TaskMisfireEnum {

	/**
	 * 默认
	 */
	DEFAULT("0"),
	/**
	 * 将所有错过的执行时间点全都补上，例如，任务15s执行一次，执行的任务错过了4分钟，则执行MisFire时，一次性执行4*(60/15)=16次任务
	 */
	IGNORE_MISFIRES("1"),
	/**
	 * 立即执行一次，然后按照Cron定义时间点执行
	 */
	FIRE_AND_PROCEED("2"),
	/**
	 * 什么都不做，等待Cron定义下次任务执行的时间点
	 */
	DO_NOTHING("3"),
	//
	;

	public final String value;

	TaskMisfireEnum(String value) {
		this.value = value;
	}

	/**
	 * @param value
	 * @return
	 */
	public static TaskMisfireEnum getEnum(String value) {
		if (StringUtils.isBlank(value)) {
			return TaskMisfireEnum.DEFAULT;
		}
		for (TaskMisfireEnum anEnum : TaskMisfireEnum.values()) {
			if (anEnum.value.equals(value)) {
				return anEnum;
			}
		}
		return TaskMisfireEnum.DEFAULT;
	}
}
