package com.sansec.ai.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 异步任务状态
 *
 * <AUTHOR>
 * @since 2025-2-24
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum {
	/**
	 * NEW
	 */
	NEW(0, "未执行"),
	/**
	 * RUNNABLE
	 */
	RUNNABLE(1, "执行中"),
	/**
	 * SUCCESS
	 */
	SUCCESS(2, "成功"),
	/**
	 * FAIL
	 */
	FAIL(3, "异常"),
	/**
	 * timeout
	 */
	TIMEOUT(4, "超时"),
	;
	private final Integer taskStatus;
	private final String msg;

}