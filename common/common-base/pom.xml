<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.sansec.ai</groupId>
		<artifactId>ai-unified-portal</artifactId>
		<version>1.0-SNAPSHOT</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>

	<artifactId>common-base</artifactId>

	<properties>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<sonar.projectKey>common-base</sonar.projectKey>
		<sonar.projectName>common-base</sonar.projectName>
	</properties>


	<dependencies>
		<dependency>
			<groupId>com.sansec.springboot</groupId>
			<artifactId>sansec-springboot-dependencies</artifactId>
			<exclusions>
				<exclusion>
					<groupId>commons-beanutils</groupId>
					<artifactId>commons-beanutils</artifactId>
				</exclusion>
                <exclusion>
                    <groupId>org.xerial</groupId>
                    <artifactId>sqlite-jdbc</artifactId>
                </exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.sansec.db</groupId>
			<artifactId>sansec-db</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sansec.component.algorithm</groupId>
			<artifactId>sansec-component-algorithm</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-config</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sansec.common</groupId>
			<artifactId>sansec-common</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>commons-lang3</artifactId>
					<groupId>org.apache.commons</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
		</dependency>
	</dependencies>

</project>