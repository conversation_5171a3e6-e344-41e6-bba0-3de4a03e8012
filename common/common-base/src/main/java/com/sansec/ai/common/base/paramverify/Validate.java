package com.sansec.ai.common.base.paramverify;

/**
 * 自定义参数校验规则接口
 *
 * <AUTHOR>
 * @date 2022/11/15 10:39
 */
public interface Validate {

    /**
     * 根据入参校验参数合法性
     *
     * @return 成功则返回空串或null， 否则返回错误信息描述
     */
    String validate();


    /**
     * @param @param  obj1
     * @param @param  obj2
     * @param @return 参数
     * @return Boolean 返回类型
     * @throws
     * @Title: isEqual
     * @Description: 比较是否相同
     */
    default Boolean isEqual(Object obj1, Object obj2) {

        if ((obj1 == null || ("").equals(obj1)) && (obj2 == null || ("").equals(obj2))) {
            return true;
        }
        if (obj1 != null && obj2 != null && obj1.equals(obj2)) {
            return true;
        }
        return false;
    }

}
