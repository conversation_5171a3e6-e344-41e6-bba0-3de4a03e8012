//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.sansec.ai.common.base.utils;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.sansec.ai.common.base.enums.TokenTypeEnum;
import com.sansec.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Component
public class TokenUtil {
	private static String TOKEN_KEY;

	@Value("${secplat.base.token}")
	public void setTokenKey(String tokenKey) {
		TOKEN_KEY = tokenKey;
	}

	public static String createToken(Long userId, String userType, String portalCode) {
		Map<String, Object> map = new HashMap();
		map.put("userId", userId);
		map.put("userType", userType);
		map.put("uuid", UUID.randomUUID().toString().replace("-", ""));
		map.put("portalCode", portalCode);
		return JWTUtil.createToken(map, TOKEN_KEY.getBytes());
	}

	public static JWT parseToken(String token) {
		boolean flag = JWTUtil.verify(token, TOKEN_KEY.getBytes());
		if (!flag) {
			throw new BusinessException("00020305");
		} else {
			return JWTUtil.parseToken(token);
		}
	}


	public static String getTokenFromHeader(TokenTypeEnum tokenType) {
		ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		String headName = tokenType == TokenTypeEnum.SEC_PLAT_TOKEN ? "SecToken" : "AiPortalToken";
		return attributes != null ? attributes.getRequest().getHeader(headName) : "";
	}

	private static Object getPayloadFromToken(String token, String key) {
		if (StringUtils.isBlank(token)) {
			return null;
		}
		boolean flag = JWTUtil.verify(token, TOKEN_KEY.getBytes());
		if (!flag) {
			return null;
		}

		JWT jwt = JWTUtil.parseToken(token);
		return jwt.getPayload().getClaim(key);
	}

	public static Long getUserId(TokenTypeEnum tokenType) {
		Object userId = getPayloadFromToken(getTokenFromHeader(tokenType), "userId");
		if (userId == null) {
			return null;
		}
		return NumberUtil.parseLong(String.valueOf(userId));
	}

	public static String getPortalCode(TokenTypeEnum tokenType) {
		Object userId = getPayloadFromToken(getTokenFromHeader(tokenType), "portalCode");
		if (userId == null) {
			return null;
		}
		return String.valueOf(userId);
	}

	public static String getTenantCode(String secToken) {
		if (StringUtils.isBlank(secToken)) {
			return null;
		}

		JWT jwt = JWTUtil.parseToken(secToken);
		Object tenantCode = jwt.getPayload().getClaim("tenantCode");
		if (tenantCode == null) {
			return null;
		}
		return String.valueOf(tenantCode);
	}

	private TokenUtil() {
	}
}
