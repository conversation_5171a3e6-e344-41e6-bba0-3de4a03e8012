package com.sansec.ai.common.base.utils;


import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 请求头工具类，用于从当前请求中获取header信息
 */
public class RequestHeaderUtils {

    private RequestHeaderUtils() {}

    /**
     * 获取当前请求对象
     *
     * @return HttpServletRequest 当前请求对象，如果不在请求上下文中返回null
     */
    public static HttpServletRequest getRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return attributes == null ? null : attributes.getRequest();
    }

    /**
     * 检查当前请求头中lang字段是否等于zh_CN
     * @return true-是中文 false-不是中文
     */
    public static boolean isHeaderLangEqCn() {
        return "zh_CN".equals(getHeader("lang"));
    }

    public static String getLangFromHeader() {
        return getHeader("lang");
    }

    /**
     * 获取指定名称的请求头
     *
     * @param headerName 请求头名称
     * @return 请求头的值，如果不存在或不在请求上下文中则返回null
     */
    public static String getHeader(String headerName) {
        HttpServletRequest request = getRequest();
        return request == null ? null : request.getHeader(headerName);
    }

    /**
     * 获取所有请求头
     *
     * @return 包含所有请求头的Map，如果不在请求上下文中则返回空Map
     */
    public static Map<String, String> getAllHeaders() {
        Map<String, String> headers = new HashMap<>();
        HttpServletRequest request = getRequest();

        if (request != null) {
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                headers.put(headerName, request.getHeader(headerName));
            }
        }

        return headers;
    }

    /**
     * 获取请求头，带默认值
     *
     * @param headerName  请求头名称
     * @param defaultValue 默认值
     * @return 请求头的值，如果不存在则返回默认值
     */
    public static String getHeaderOrDefault(String headerName, String defaultValue) {
        String value = getHeader(headerName);
        return value == null ? defaultValue : value;
    }

    /**
     * 检查请求头是否存在
     *
     * @param headerName 请求头名称
     * @return 如果请求头存在返回true，否则返回false
     */
    public static boolean containsHeader(String headerName) {
        return getHeader(headerName) != null;
    }
}