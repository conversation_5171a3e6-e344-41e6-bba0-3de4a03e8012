package com.sansec.ai.common.base.handler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 时间格式转换处理器
 *
 * <AUTHOR>
 * @since 2024/3/20 9:48
 */
public class CsDateFormatHandler extends BaseTypeHandler<Date> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, Date date, JdbcType jdbcType) throws SQLException {
        SimpleDateFormat format0 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateStr = format0.format(date);
        preparedStatement.setString(i,dateStr);
    }

    @Override
    public Date getNullableResult(ResultSet resultSet, String s) throws SQLException {
        Timestamp sqlTimestamp = resultSet.getTimestamp(s);
        return sqlTimestamp != null ? new Date(sqlTimestamp.getTime()) : null;
    }

    @Override
    public Date getNullableResult(ResultSet resultSet, int i) throws SQLException {
        Timestamp sqlTimestamp = resultSet.getTimestamp(i);
        return sqlTimestamp != null ? new Date(sqlTimestamp.getTime()) : null;
    }

    @Override
    public Date getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        Timestamp sqlTimestamp = callableStatement.getTimestamp(i);
        return sqlTimestamp != null ? new Date(sqlTimestamp.getTime()) : null;
    }
}
