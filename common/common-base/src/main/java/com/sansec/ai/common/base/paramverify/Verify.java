package com.sansec.ai.common.base.paramverify;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2022/4/6 11:11
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.FIELD})
public @interface Verify {

    /**
     * 标注属性是否为List对象
     *
     * @return
     */
    boolean isList() default false;

    /**
     * 字段参数按照指定的分隔符进行分割，按照分割之后的集合进行合法性校验
     *
     * @return
     */
    String stringSplit() default "";

    /**
     * 参数不能为null
     *
     * @return
     */
    boolean notNull() default false;

    /**
     * 参数不能为null, String类型的话，不能为空串
     *
     * @return
     */
    boolean notBlank() default false;

    /**
     * 字符串最大长度限制
     *
     * @return
     */
    int maxLength() default 1024;

    /**
     * 字符串最小长度限制
     *
     * @return
     */
    int minLength() default 0;

    /**
     * 数字类型最大值
     *
     * @return
     */
    double max() default Long.MAX_VALUE;

    /**
     * 数字类型最小值
     *
     * @return
     */
    double min() default Long.MIN_VALUE;

    /**
     * 字符串类型， 正则匹配要求
     *
     * @return
     */
    String regular() default "";

    /**
     * 数字类型可选入参列表
     *
     * @return
     */
    String[] optional() default {};

    /**
     * 字典指定属性的值 toString 后通过 equals进行比对
     *
     * @return
     */
    EnumValidate enumValidate() default @EnumValidate;

    /**
     * 校验失败后返回的错误报文
     *
     * @return
     */
    String message() default VerifyConst.FIELD_VALIDATE_FAILED;


}
