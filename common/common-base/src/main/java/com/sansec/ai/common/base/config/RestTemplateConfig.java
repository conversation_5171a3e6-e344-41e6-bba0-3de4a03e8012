package com.sansec.ai.common.base.config;

import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManagerBuilder;
import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.core5.ssl.SSLContexts;
import org.apache.hc.core5.ssl.TrustStrategy;
import org.apache.hc.core5.util.Timeout;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.BufferingClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;


/**
 *@Author：cuipengfei
 *@CreateAt: 2022-07-04  14:52
 *@Description: restemplate配置
 */
@Configuration
public class RestTemplateConfig {

    @Value("${secplat.restTemplate.readTimeout:150000}")
    Integer readTimeout;

    @Value("${secplat.restTemplate.connectTimeout:150000}")
    Integer connectTimeout;

    @Value("${secplat.restTemplate.maxConnTotal:100}")
    Integer maxConnTotal;

    @Value("${secplat.restTemplate.maxConnPerRoute:10}")
    Integer maxConnPerRoute;

    @Bean
    public RestTemplate restTemplate(@Qualifier("factory") ClientHttpRequestFactory factory) {
        return new RestTemplate(factory);
    }

    @Bean
    public RestTemplate gatewayTemplate(@Qualifier("factory") ClientHttpRequestFactory factory){
        RestTemplate restTemplate = new RestTemplate(factory);
        ResponseErrorHandler responseErrorHandler = new ResponseErrorHandler() {
            @Override
            public boolean hasError(ClientHttpResponse response) throws IOException {
                return true;
            }
            @Override
            public void handleError(ClientHttpResponse response) throws IOException {
                // 不需要实现
            }
        };
        restTemplate.setErrorHandler(responseErrorHandler);
        return restTemplate;
    }

    @Bean
    public RestTemplate restTemplateSsl(@Qualifier("sslRequestFactory") ClientHttpRequestFactory sslRequestFactory) {
        return new RestTemplate(sslRequestFactory);
    }

    @Bean
    public ClientHttpRequestFactory factory() {

        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        // 最大链接数
        connectionManager.setMaxTotal(maxConnTotal);
        // 同路由并发数20
        connectionManager.setDefaultMaxPerRoute(maxConnPerRoute);

        RequestConfig requestConfig = RequestConfig.custom()
                // 链接不够用的等待时间
                .setConnectionRequestTimeout(Timeout.ofMilliseconds(readTimeout))
                .build();


        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .build();


        HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
        httpRequestFactory.setConnectTimeout(connectTimeout);
        ClientHttpRequestFactory requestFactory = new BufferingClientHttpRequestFactory(httpRequestFactory);

        return requestFactory;
    }

    @Bean
    public ClientHttpRequestFactory sslRequestFactory() throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
        TrustStrategy acceptingTrustStrategy = (x509Certificates, authType) -> true;
        SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(null, acceptingTrustStrategy).build();
        SSLConnectionSocketFactory connectionSocketFactory = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);

        HttpClientBuilder httpClientBuilder = HttpClients.custom();

        PoolingHttpClientConnectionManager connectionManager = PoolingHttpClientConnectionManagerBuilder
                .create()
                .setSSLSocketFactory(connectionSocketFactory)
                .build();
        // 最大链接数
        connectionManager.setMaxTotal(maxConnTotal);
        // 同路由并发数20
        connectionManager.setDefaultMaxPerRoute(maxConnPerRoute);

        RequestConfig requestConfig = RequestConfig.custom()
                // 链接不够用的等待时间
                .setConnectionRequestTimeout(Timeout.ofMilliseconds(readTimeout))
                .build();

        CloseableHttpClient httpClient = httpClientBuilder
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .build();
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setHttpClient(httpClient);
        ClientHttpRequestFactory requestFactory = new BufferingClientHttpRequestFactory(factory);
        return requestFactory;
    }

}
