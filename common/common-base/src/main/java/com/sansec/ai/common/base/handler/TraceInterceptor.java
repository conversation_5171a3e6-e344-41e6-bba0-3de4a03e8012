package com.sansec.ai.common.base.handler;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * @Author：cuipengfei
 * @CreateAt: 2024-09-04  21:27
 * @Description: trace
 */
@Component
public class TraceInterceptor extends BaseInterceptorAdapter {

	private static final String TRACE_ID = "traceId";

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
		String traceId = MDC.get(TRACE_ID);
		if (traceId == null) {
			MDC.put(TRACE_ID, UUID.randomUUID().toString().replace("-", ""));
		}
		return true;
	}

	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
		MDC.remove(TRACE_ID);
	}


}
