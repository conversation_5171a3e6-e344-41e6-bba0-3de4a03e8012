package com.sansec.ai.common.base.config;


import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.sansec.ai.common.base.handler.CsMetaObjectHandler;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/3/5 13:38
 */
@Configuration
public class DataSourceConfiguration {

	@Autowired
	private MybatisPlusInterceptor mybatisPlusInterceptor;

	@jakarta.annotation.Resource
	private CsMetaObjectHandler csMetaObjectHandler;


	@Bean
	@Primary
	public SqlSessionFactory sqlSessionFactory(@Qualifier("dataSource") @Autowired(required = false) DataSource dataSource) throws Exception {
		MybatisSqlSessionFactoryBean sessionFactoryBean = new MybatisSqlSessionFactoryBean();
		sessionFactoryBean.setDataSource(dataSource);
		GlobalConfig globalConfig = new GlobalConfig();
		// 设置Mapper文件的扫描路径
		String mapperLocations = "classpath*:ai/*/*Mapper.xml";
		String systemMapperLocations = "classpath*:system/*/*Mapper.xml";
		Resource[] resources = new PathMatchingResourcePatternResolver().getResources(mapperLocations);
		Resource[] systemResources = new PathMatchingResourcePatternResolver().getResources(systemMapperLocations);
		Resource[] combinedResources = Arrays.copyOf(resources, resources.length + systemResources.length);
		System.arraycopy(systemResources, 0, combinedResources, resources.length, systemResources.length);
		sessionFactoryBean.setMapperLocations(combinedResources);
		sessionFactoryBean.setPlugins(mybatisPlusInterceptor);
		globalConfig.setMetaObjectHandler(csMetaObjectHandler);
		sessionFactoryBean.setGlobalConfig(globalConfig);
		return sessionFactoryBean.getObject();
	}


	@Bean
	@Primary
	public SqlSessionTemplate sqlSessionTemplate(@Qualifier("sqlSessionFactory") SqlSessionFactory systemSqlSessionFactory) {
		return new SqlSessionTemplate(systemSqlSessionFactory);
	}

	@Bean
	@Primary
	public PlatformTransactionManager platformTransactionManager(@Qualifier("dataSource") @Autowired DataSource dataSource) {
		return new DataSourceTransactionManager(dataSource);
	}

	@Bean("systemTransactionManager")
	public PlatformTransactionManager systemTransactionManager(@Qualifier("dataSource") @Autowired DataSource dataSource) {
		return new DataSourceTransactionManager(dataSource);
	}
}
