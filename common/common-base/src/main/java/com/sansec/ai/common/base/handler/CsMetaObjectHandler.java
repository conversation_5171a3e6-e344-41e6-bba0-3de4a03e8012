package com.sansec.ai.common.base.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.sansec.ai.common.base.enums.EnumDelFlag;
import com.sansec.ai.common.base.enums.TokenTypeEnum;
import com.sansec.ai.common.base.utils.TokenUtil;
import com.sansec.common.id.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 元数据填充
 *
 * <AUTHOR>
 * @since 2024/3/13 14:24
 */
@Slf4j
@Component
public class CsMetaObjectHandler implements MetaObjectHandler {

	@Override
	public void insertFill(MetaObject metaObject) {
		log.info("start insert fill ....");
		this.strictInsertFill(metaObject, "id", Long.class, IdGenerator.ins().generator());
		this.strictInsertFill(metaObject, "flagDel", Integer.class, EnumDelFlag.NORMAL.getKey());
		this.strictInsertFill(metaObject, "createTime", Date.class, new Date());
		this.strictInsertFill(metaObject, "updateTime", Date.class, new Date());
		this.strictInsertFill(metaObject, "createBy", Long.class, getCurrentUser());
		this.strictInsertFill(metaObject, "updateBy", Long.class, getCurrentUser());
	}

	@Override
	public void updateFill(MetaObject metaObject) {
		log.info("start update fill ....");
		this.setFieldValByName("updateTime", new Date(), metaObject);
		this.setFieldValByName("updateBy", getCurrentUser(), metaObject);
	}

	/**
	 * 获取操作用户，优先默认系统用户
	 *
	 * @return 当前登录用户id
	 */
	private Long getCurrentUser() {
		Long currentUser = null;
		try {
			currentUser = TokenUtil.getUserId(TokenTypeEnum.SEC_PLAT_TOKEN);
			if (currentUser == null) {
				currentUser = TokenUtil.getUserId(TokenTypeEnum.AI_PORTAL_TOKEN);
			}
		} catch (Exception e) {
			// do nothing
		}
		return currentUser;
	}
}
