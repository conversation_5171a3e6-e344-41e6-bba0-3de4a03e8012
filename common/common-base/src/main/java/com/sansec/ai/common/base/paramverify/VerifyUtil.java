package com.sansec.ai.common.base.paramverify;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.sansec.swplugin.common.utils.FieldUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 标注到属性上，标识该属性需要校验其内部属性的合法性
 *
 * <AUTHOR>
 * @date 2022/4/6 11:37
 */
@Slf4j
public class VerifyUtil {

    /**
     * 入参合法性检查
     *
     * @param param
     * @return
     */
    public static String check(Object param) {
        Assert.notNull(param, "VerifyUtil check param must be not null");

        Map<Class<? extends Annotation>, List<Field>> fieldMap = FiledParser.getFieldMap(param);

        String validate = "";
        if (fieldMap.containsKey(Verify.class)) {
            validate = FieldValidator.validate(fieldMap.get(Verify.class), param);
            if (!StrUtil.isEmpty(validate)) {
                return validate;
            }
        }

        if (fieldMap.containsKey(Check.class)) {
            List<Field> fields = fieldMap.get(Check.class);
            for (Field field : fields) {
                Object fieldObj = FieldUtil.getField(param, field);
                if (fieldObj == null) {
                    continue;
                }
                Check check = field.getAnnotation(Check.class);
                if (fieldObj instanceof List && check != null && check.isList()) {
                    validate = checkList((List<Object>) fieldObj);
                } else {
                    validate = check(fieldObj);
                }
                if (!StrUtil.isEmpty(validate)) {
                    return validate;
                }
            }
        }

        String invoke = checkValidate(param);
        if (StrUtil.isNotEmpty(invoke)) {
            return invoke;
        }

        return validate;
    }

    private static String checkValidate(Object param) {
        boolean match = Arrays.stream(param.getClass().getInterfaces()).anyMatch(Validate.class::equals);
        if (match) {
            try {
                Method method = param.getClass().getMethod("validate");
                if (!method.isAccessible()) {
                    method.setAccessible(true);
                }
                Object invoke = method.invoke(param);
                if (invoke instanceof String && StrUtil.isNotEmpty((String) invoke)) {
                    return (String) invoke;
                }
            } catch (Exception e) {
                log.error("Validate param with Validate class failed. Error message: ", e);
                return VerifyConst.FIELD_VALIDATE_FAILED;
            }
        }
        return null;
    }

    public static String checkList(List<Object> paramList) {
        String validate = "";
        for (Object param : paramList) {
            validate = check(param);
            if (StrUtil.isNotEmpty(validate)) {
                return validate;
            }
        }
        return validate;
    }

    /**
     * 属性合法性校验器
     */
    static class FieldValidator {

        public static String validate(List<Field> fields, Object param) {
            for (Field field : fields) {
                String validate = validate(field, param);
                if (StrUtil.isNotEmpty(validate)) {
                    return validate;
                }
            }
            return "";
        }

        public static String validate(Field field, Object param) {
            Verify verify = field.getAnnotation(Verify.class);

            Object obj = FieldUtil.getField(param, field);

            if (!verify.enumValidate().enums().equals(Object.class)) {
                return enumValidate(field, obj, verify);
            }

            if (field.getType().equals(String.class) && StrUtil.isEmpty(verify.stringSplit())) {
                return stringValidate(field, (String) obj, verify);
            }

            if (isNumericType(field)) {
                return numericValidate(field, obj, verify);
            }

            if (field.getType().equals(String.class) && StrUtil.isNotEmpty(verify.stringSplit())) {
                if (verify.notNull() && obj == null) {
                    return getErrorMsg(field, "filed must not be null", verify, obj);
                }

                if (verify.notBlank() && StrUtil.isEmpty((String) obj)) {
                    return getErrorMsg(field, "filed must not be blank", verify, obj);
                }

                if (StrUtil.isEmpty((String) obj)) {
                    return "";
                }

                String tmpObj = (String) obj;
                List<Object> strings = Arrays.stream(tmpObj.split(verify.stringSplit())).collect(Collectors.toList());
                return listValidate(field, strings, verify);
            }

            if (field.getType().equals(List.class) && verify.isList()) {
                return listValidate(field, (List<Object>) obj, verify);
            }
            if (field.getType().equals(Set.class) && verify.isList()) {
                Set<?> set = (Set<?>) obj;
                return listValidate(field, new ArrayList<>(set), verify);
            }

            return objectValidate(field, obj, verify);
        }

        private static String enumValidate(Field field, Object obj, Verify verify) {
            if ((verify.notNull() || verify.notBlank()) && obj == null) {
                return getErrorMsg(field, field.getName() + " must not be null", verify, obj);
            }

            if (!verify.notNull() && !verify.notBlank() && obj == null) {
                return "";
            }

            Class<?> enums = verify.enumValidate().enums();
            Enum[] enumConstants = (Enum[]) enums.getEnumConstants();
            try {
                List<String> fieldValues = new ArrayList<>();
                for (Enum anEnum : enumConstants) {
                    Field declaredField = anEnum.getClass().getDeclaredField(verify.enumValidate().field());
                    declaredField.setAccessible(true);
                    Object o = declaredField.get(anEnum);
                    fieldValues.add(o.toString());
                }
                boolean contains = fieldValues.contains(obj.toString());
                if (contains) {
                    return "";
                }

                String[] strings = new String[fieldValues.size()];
                for (int i = 0; i < fieldValues.size(); i++) {
                    strings[i] = fieldValues.get(i).toString();
                }
                return getErrorMsg(field, "optional value: " + String.join("/", strings), verify, obj);

            } catch (NoSuchFieldException | IllegalAccessException e) {
                return "no enum field found";
            }
        }

        private static String objectValidate(Field field, Object obj, Verify verify) {
            if (verify.notNull() && obj == null) {
                return getErrorMsg(field, "field must not be null", verify, obj);
            }
            if ((field.getType().equals(Set.class) || field.getType().equals(List.class))
                    && ((obj == null || CollectionUtil.isEmpty((List<Object>) obj)) && verify.notBlank())) {
                return getErrorMsg(field, "field must not be blank", verify, obj);
            }

            if (field.getType().equals(Map.class) && ((obj == null || ((Map) obj).isEmpty()) && verify.notBlank())) {
                return getErrorMsg(field, "field must not be blank", verify, obj);
            }
            return "";
        }

        private static String listValidate(Field field, List<Object> objects, Verify verify) {
            if (verify.notNull() && objects == null) {
                return getErrorMsg(field, "filed must not be null", verify, objects);
            }
            if (CollectionUtil.isEmpty(objects) && verify.notBlank()) {
                return getErrorMsg(field, "filed must not be blank", verify, objects);
            }
            if (CollectionUtil.isEmpty(objects)) {
                return "";
            }

            Object param = objects.get(0);
            Class<?> tClass = param.getClass();
            if (isNumericType(tClass)) {
                for (Object object : objects) {
                    String validate = numericValidate(field, object, verify);
                    if (StrUtil.isNotEmpty(validate)) {
                        return validate;
                    }
                }
            }

            if (tClass.equals(String.class)) {
                for (Object object : objects) {
                    String validate = stringValidate(field, (String) object, verify);
                    if (StrUtil.isNotEmpty(validate)) {
                        return validate;
                    }
                }
            }

            return "";
        }

        private static String numericValidate(Field field, Object param, Verify verify) {

            if ((verify.notNull() || verify.notBlank()) && param == null) {
                return getErrorMsg(field, field.getName() + " must not be null", verify, param);
            }

            if (!verify.notNull() && !verify.notBlank() && param == null) {
                return "";
            }

            if (verify.optional().length > 0) {
                String message = getErrorMsg(field, verify.message(), verify, param);
                return Arrays.asList(verify.optional()).contains(NumberUtil.toStr((Number) param)) ? "" : message;
            }

            BigDecimal paramDecimal = NumberUtil.toBigDecimal((Number) param);
            BigDecimal maxDecimal = BigDecimal.valueOf(verify.max());
            if (maxDecimal.compareTo(paramDecimal) < 0 || BigDecimal.valueOf(verify.min()).compareTo(paramDecimal) > 0) {
                return getErrorMsg(field, field.getName() + " the value range is between " + verify.min() + " and " + verify.max(), verify, param);
            }
            return "";
        }

        private static String stringValidate(Field field, String param, Verify verify) {

            if (!verify.notNull() && !verify.notBlank() && StrUtil.isEmpty(param)) {
                return "";
            }

            if (verify.optional().length > 0) {
                String message = getErrorMsg(field, verify.message(), verify, param);
                return Arrays.asList(verify.optional()).contains(param) ? "" : message;
            }

            if ((verify.notNull() && param == null) || (verify.notBlank() && StrUtil.isEmpty(param))
                    || (param.length() > verify.maxLength()) || (param.length() < verify.minLength()) || !regularMatch(param, verify.regular())) {
                return getErrorMsg(field, verify.message(), verify, param);
            }
            return "";
        }

        private static String getErrorMsg(Field field, String message, Verify verify, Object param) {
            if (VerifyConst.FIELD_VALIDATE_FAILED.equals(verify.message()) && verify.optional().length > 0) {
                return field.getName() + "=" + (param instanceof String ? param : JSON.toJSONString(param)) + " optional content: " + String.join("/", verify.optional());
            }
            if (VerifyConst.FIELD_VALIDATE_FAILED.equals(verify.message())) {
                return field.getName() + "=" + (param instanceof String ? param : JSON.toJSONString(param)) + " " + message;
            }
            return field.getName() + "=" + (param instanceof String ? param : JSON.toJSONString(param)) + " " + verify.message();
        }

        private static boolean isNumericType(Field field) {
            return field.getType().equals(Integer.class) || field.getType().equals(int.class) || field.getType().equals(Double.class) || field.getType().equals(double.class)
                    || field.getType().equals(Short.class) || field.getType().equals(short.class) || field.getType().equals(Long.class) || field.getType().equals(long.class)
                    || field.getType().equals(Float.class) || field.getType().equals(float.class);
        }

        private static boolean isNumericType(Class<?> tClass) {
            return tClass.equals(Integer.class) || tClass.equals(int.class) || tClass.equals(Double.class) || tClass.equals(double.class)
                    || tClass.equals(Short.class) || tClass.equals(short.class) || tClass.equals(Long.class) || tClass.equals(long.class)
                    || tClass.equals(Float.class) || tClass.equals(float.class);
        }

        private static boolean regularMatch(String param, String regular) {
            if (StrUtil.isEmpty(regular)) {
                return true;
            }
            if (StrUtil.isEmpty(param)) {
                return false;
            }

            Pattern pattern = Pattern.compile(regular);
            Matcher matcher = pattern.matcher(param);
            return matcher.find();
        }

    }

    /**
     * 负责解析入参属性信息
     */
    static class FiledParser {

        public static Map<Class<?>, Map<Class<? extends Annotation>, List<Field>>> fieldCache = new ConcurrentHashMap<>();

        /**
         * 获取指定继承链上所有标注了指定注解的属性
         *
         * @param param
         * @return
         */
        public static Map<Class<? extends Annotation>, List<Field>> getFieldMap(Object param) {
            if (fieldCache.containsKey(param.getClass())) {
                return fieldCache.get(param.getClass());
            }
            Set<Field> fields = findFields(param, Verify.class, Check.class);
            Map<Class<? extends Annotation>, List<Field>> fieldMap = parseAnn(fields, Verify.class, Check.class);
            fieldCache.put(param.getClass(), fieldMap);
            return fieldMap;
        }

        /**
         * 解析
         *
         * @param fields
         * @param filter
         * @return
         */
        @SafeVarargs
        private static Map<Class<? extends Annotation>, List<Field>> parseAnn(Set<Field> fields, Class<? extends Annotation>... filter) {
            Map<Class<? extends Annotation>, List<Field>> result = new HashMap<>();
            for (Field field : fields) {
                for (Class<? extends Annotation> aClass : filter) {
                    if (field.isAnnotationPresent(aClass)) {
                        result.computeIfAbsent(aClass, n -> new ArrayList<>()).add(field);
                    }
                }
            }
            return result;
        }

        /**
         * 获取继承链上所有属性
         *
         * @param param
         * @return
         */
        @SafeVarargs
        private static Set<Field> findFields(Object param, Class<? extends Annotation>... filter) {
            Class<?> tClass = param.getClass();
            Set<Field> clazzSet = new HashSet<>(Arrays.asList(tClass.getDeclaredFields()));
            Class<?> tmpClass = tClass;
            while (tmpClass != null) {
                final Class<?> superclass = tmpClass.getSuperclass();
                if (superclass != null) {
                    for (Field field : superclass.getDeclaredFields()) {
                        if (isAnnotation(field, filter)) {
                            clazzSet.add(field);
                        }
                    }
                    clazzSet.addAll(Arrays.asList(superclass.getDeclaredFields()));
                }
                tmpClass = superclass;
            }
            return clazzSet;
        }

        /**
         * 检查指定Field是否被指定注解标注， 多个注解 为 或的关系
         *
         * @param field
         * @param filter
         * @return
         */
        private static boolean isAnnotation(Field field, Class<? extends Annotation>... filter) {
            FieldUtil.accessibleField(field);
            if (filter == null) {
                return true;
            }
            for (Class<? extends Annotation> aClass : filter) {
                if (field.isAnnotationPresent(aClass)) {
                    return true;
                }
            }
            return false;
        }
    }

}
