package com.sansec.ai.common.base.config;

import com.sansec.ai.common.base.handler.BaseInterceptorAdapter;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;

/**
 * 跨域配置
 *
 * <AUTHOR>
 * @since 2024/6/21 11:35
 */
@Configuration
public class WebMVCConfig implements WebMvcConfigurer {

	@Resource(name = "traceInterceptor")
	private BaseInterceptorAdapter traceInterceptor;
	/**
	 * 门户管理拦截器
	 */
	@Resource(name = "managerInterceptor")
	private BaseInterceptorAdapter managerInterceptor;
	/**
	 * 统一门户拦截器
	 */
	@Resource(name = "portalInterceptor")
	private BaseInterceptorAdapter portalInterceptor;


	@Override
	public void addCorsMappings(CorsRegistry registry) {
		registry.addMapping("/**")//项目中的所有接口都支持跨域
				.allowedOriginPatterns("*")//所有地址都可以访问，也可以配置具体地址
				.allowCredentials(true)
				.allowedMethods("*")//"GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS"
				.maxAge(3600);// 跨域允许时间
	}

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		// 追踪拦截器
		registry.addInterceptor(traceInterceptor);
		// 门户管理拦截器
		registry.addInterceptor(managerInterceptor).addPathPatterns("/manager/**");
		// 统一门户拦截器
		registry.addInterceptor(portalInterceptor)
				.addPathPatterns(
						"/hub/**"
				)
				.excludePathPatterns(
						"/hub/ldap/login",
						"/hub/user/**"
				);
	}
}
