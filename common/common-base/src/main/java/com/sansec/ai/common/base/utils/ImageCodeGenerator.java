package com.sansec.ai.common.base.utils;


import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.ShearCaptcha;
import cn.hutool.captcha.generator.MathGenerator;
import cn.hutool.captcha.generator.RandomGenerator;
import com.sansec.ai.common.base.entity.ImageCode;

import java.awt.image.BufferedImage;


/**
 * 图片验证码生成器
 *
 * <AUTHOR>
 * @date 2022/5/18 14:53
 */
public class ImageCodeGenerator {

    /**
     * 生成图片对象
     * @return ImageCode
     */
    public static ImageCode generate() {
        RandomGenerator randomGenerator = new RandomGenerator("123456789abcdefhikmnpqrstuvwxyz", 4);
        ShearCaptcha captcha = CaptchaUtil.createShearCaptcha(116, 48, 4, 2);
        captcha.setGenerator(randomGenerator);
        BufferedImage image = captcha.getImage();
        return new ImageCode(captcha.getCode(), image);
    }

    /**
     * 验证加减乘除的运算
     * @param code 生成的code
     * @param userInputCode 用户输入的code
     * @return 成功或者失败
     */
    public static boolean verify(String code,String userInputCode){
        MathGenerator mathGenerator = new MathGenerator();
        return mathGenerator.verify(code,userInputCode);
    }
}
