package com.sansec.ai.common.base.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis-Plus配置类
 * 配置分页插件和数据库类型
 *
 * <AUTHOR> Assistant
 * @since 2025/1/20
 */
@Configuration
public class MybatisPlusConfig {

    @Value("${mybatis.databaseType:mysql}")
    private String databaseType;

    /**
     * MyBatis-Plus分页插件配置
     * 根据配置的数据库类型自动选择对应的分页方言
     *
     * @return MybatisPlusInterceptor 分页拦截器
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        
        // 根据数据库类型配置分页插件
        DbType dbType;
        if (StringUtils.isBlank(databaseType)) {
            // 默认使用MySQL
            dbType = DbType.MYSQL;
        } else {
            // 根据配置获取数据库类型
            dbType = DbType.getDbType(databaseType);
        }
        
        // 添加分页内部拦截器
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor(dbType);
        // 设置最大单页限制数量，默认 500 条，-1 不受限制
        paginationInnerInterceptor.setMaxLimit(500L);
        // 溢出总页数后是否进行处理(默认不处理)
        paginationInnerInterceptor.setOverflow(false);
        
        interceptor.addInnerInterceptor(paginationInnerInterceptor);
        
        return interceptor;
    }
}
