package com.sansec.ai.common.base.enums;

/**
 * 删除状态枚举
 *
 * <AUTHOR>
 * @since 2024/3/13 14:24
 */
public enum EnumDelFlag {

    NORMAL(0, "Normal"),
    DELETED(1, "Delete");

    EnumDelFlag(Integer i, String value) {
        this.key = i;
        this.value = value;
    }

    private final Integer key;
    private final String value;

    public String getValue() {
        return value;
    }

    public Integer getKey() {
        return key;
    }

}
