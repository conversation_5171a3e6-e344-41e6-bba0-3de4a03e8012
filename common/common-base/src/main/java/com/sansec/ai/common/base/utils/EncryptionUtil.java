package com.sansec.ai.common.base.utils;

import com.sansec.component.algorithm.utill.ComponentSynthesisEncryptionUtil;

/**
 * <AUTHOR>
 * @since 2025/5/7 11:10
 */
public class EncryptionUtil {

	/**
	 * 使用对称密钥对数据加密
	 *
	 * @param plaintext 明文数据
	 * @return
	 */
	public static String encode(String plaintext) {
		String encodePasswd = ComponentSynthesisEncryptionUtil.encPwd(plaintext);
		return encodePasswd;
	}

	/**
	 * 使用对称密钥对数据解密
	 *
	 * @param ciphertext 密文数据
	 * @return
	 */
	public String decode(String ciphertext) {
		return ComponentSynthesisEncryptionUtil.decPwd(ciphertext);
	}

}
