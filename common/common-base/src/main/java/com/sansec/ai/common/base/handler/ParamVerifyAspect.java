package com.sansec.ai.common.base.handler;

import cn.hutool.json.JSONUtil;
import com.sansec.ai.common.base.constant.ErrorCode;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import com.sansec.ai.common.base.paramverify.VerifyUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.List;

/**
 * 参数校验切面
 *
 * <AUTHOR>
 * @date 2023/9/14 15:46
 */
@Aspect
@Slf4j
@Order(4)
@Component
public class ParamVerifyAspect {


    /**
     * 定义切面
     */
    @Pointcut("execution(public * com.sansec.ai.*.*.controller.*.*(..))")
    public void verify() {
        // default implementation ignored
    }

    /**
     * 方法执行之前执行
     *
     * @return
     */
    @Around("verify()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature sign = (MethodSignature) joinPoint.getSignature();
        Method method = sign.getMethod();
        // 如果不是Controller的方法，则忽略
        if (!method.isAnnotationPresent(RequestMapping.class) && !method.isAnnotationPresent(GetMapping.class)
                && !method.isAnnotationPresent(PostMapping.class)) {
            return joinPoint.proceed();
        }

        Object paramObj = null;
        Parameter[] parameters = method.getParameters();
        for (int i = 0; i < parameters.length; i++) {
            if (parameters[i].isAnnotationPresent(RequestBody.class)) {
                paramObj = joinPoint.getArgs()[i];
                break;
            }
        }
        if (paramObj == null) {
            return joinPoint.proceed();
        }

        String res;
        if (paramObj instanceof List) {
            res = VerifyUtil.checkList((List<Object>) paramObj);
        } else {
            res = VerifyUtil.check(paramObj);
        }
        if (StringUtils.hasText(res)) {
            log.error("param verify failed. method: {}, param: {}, error message: {}", method.getName(), JSONUtil.toJsonStr(joinPoint.getArgs()), res);
            final SecRestResponse<Object> restResponse = ResultUtil.error(ErrorCode.PARAM_VERIFY_FAILED);
            restResponse.setResult(res);
            return restResponse;
        }
        return joinPoint.proceed();
    }

}
