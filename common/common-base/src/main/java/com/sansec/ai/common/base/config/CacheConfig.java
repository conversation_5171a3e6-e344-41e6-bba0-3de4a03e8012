package com.sansec.ai.common.base.config;



import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Expiry;
import com.sansec.ai.common.base.entity.ImageCode;
import com.sansec.ai.common.base.entity.UserLock;
import org.checkerframework.checker.index.qual.NonNegative;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 *@Author：cuipengfei
 *@CreateAt: 2022-05-18  10:56
 *@Description: 缓存配置
 */
@Configuration
public class CacheConfig {
    @Value("${ai.cache.user.tokenExpireMinute:30}")
    public long userTokenDuration;

    @Value("${ai.cache.user.captchaExpireMinute:1}")
    public long captchaExpireMinute;

    /**
     * 用户token缓存对象
     * @return 缓存
     */
    @Bean
    public Cache<String, Object> portalUserCache() {
        return Caffeine.newBuilder()
                // 设置最后一次写入或访问后经过固定时间过期
                .expireAfter(new Expiry<Object, Object>() {
                    @Override
                    public long expireAfterCreate(@NonNull Object key, @NonNull Object value, long currentTime) {
                        return getUserTokenExpireNanos();
                    }

                    @Override
                    public long expireAfterUpdate(@NonNull Object key, @NonNull Object value, long currentTime, @NonNegative long currentDuration) {
                        return getUserTokenExpireNanos();
                    }

                    @Override
                    public long expireAfterRead(@NonNull Object key, @NonNull Object value, long currentTime, @NonNegative long currentDuration) {
                        return currentTime;
                    }
                })
                // 初始的缓存空间大小
                .initialCapacity(100)
                // 缓存的最大条数
                .maximumSize(1000)
                .build();
    }

    private long getUserTokenExpireNanos() {
        return TimeUnit.MINUTES.toNanos(userTokenDuration);
    }

    /**
     * 用户验证码缓存对象
     * @return 缓存
     */
    @Bean
    public Cache<String, ImageCode> portalVerifyCodeCache() {
        return Caffeine.newBuilder()
                // 设置最后一次写入或访问后经过固定时间过期
                .expireAfterWrite(captchaExpireMinute, TimeUnit.MINUTES)
                // 初始的缓存空间大小
                .initialCapacity(100)
                // 缓存的最大条数
                .maximumSize(1000)
                .build();
    }

    /**
     * 锁定缓存对象
     * @return 缓存
     */
    @Bean
    public Cache<String, UserLock> portalLockAccountCache() {

        return Caffeine.newBuilder()
                // 设置最后一次写入或访问后经过固定时间过期
//                .expireAfterWrite(60, TimeUnit.SECONDS)
                // 初始的缓存空间大小
                .initialCapacity(100)
                // 缓存的最大条数
                .maximumSize(1000)
                .build();
    }

    /**
     * 应用图标URL缓存
     * @return 缓存
     */
    @Bean
    public Cache<String, String> appIconUrlCache() {
        return Caffeine.newBuilder()
                // 设置最后一次写入或访问后经过固定时间过期
                .expireAfterWrite(4, TimeUnit.MINUTES)
                // 初始的缓存空间大小
                .initialCapacity(100)
                // 缓存的最大条数
                .maximumSize(1000)
                .build();
    }

}
