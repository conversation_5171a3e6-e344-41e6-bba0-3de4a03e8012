package com.sansec.ai.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ai.common.base.entity.BaseEntity;
import com.sansec.ai.common.base.enums.EnumDelFlag;
import com.sansec.ai.common.constant.IConfigCode;
import com.sansec.ai.common.entity.po.SysConfig;
import com.sansec.ai.common.mapper.SysConfigMapper;
import com.sansec.ai.common.service.SysConfigService;
import com.sansec.component.algorithm.utill.ComponentSynthesisEncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统配置项
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
@Service
@Slf4j
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements SysConfigService {

	/**
	 * 获取配置值<br>
	 * 如果有多个，只返回一个
	 *
	 * @param code
	 * @return
	 */
	@Override
	public String getConfigValue(IConfigCode code) {
		List<SysConfig> list = baseMapper.selectList(new LambdaQueryWrapper<SysConfig>()
				.eq(BaseEntity::getFlagDel, EnumDelFlag.NORMAL.getKey())
				.eq(code.getTypeCode() != null, SysConfig::getTypeCode, code.getTypeCode())
				.eq(code.getCode() != null, SysConfig::getCode, code.getCode()));
		if (CollUtil.isEmpty(list)) {
			return null;
		}
		return getConfigValue(list.get(0));
	}

	/**
	 * 获取配置项编码和配置值的集合
	 *
	 * @param code
	 * @return Map<配置项编码, 配置值>
	 */
	@Override
	public Map<String, String> getConfigValueMap(IConfigCode code) {
		List<SysConfig> list = baseMapper.selectList(new LambdaQueryWrapper<SysConfig>()
				.eq(BaseEntity::getFlagDel, EnumDelFlag.NORMAL.getKey())
				.eq(code.getTypeCode() != null, SysConfig::getTypeCode, code.getTypeCode())
				.eq(code.getCode() != null, SysConfig::getCode, code.getCode()));
		if (CollUtil.isEmpty(list)) {
			return new HashMap<>(0);
		}
		return list.stream().collect(Collectors.toMap(SysConfig::getCode, this::getConfigValue));
	}


	private String getConfigValue(SysConfig config) {
		if (config.getFlagEncrypt() != 1) {
			return config.getConfigValue();
		}
		//解密
		return ComponentSynthesisEncryptionUtil.decPwd(config.getConfigValue());
	}
}