package com.sansec.ai.common.entity.po;

import com.baomidou.mybatisplus.annotation.*;
import com.sansec.ai.common.base.entity.BaseEntity;
import lombok.Data;

/**
 * 系统配置
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
@TableName("sys_config")
@Data
public class SysConfig extends BaseEntity {
	/**
	 * ID
	 */
	@TableId(type = IdType.INPUT)
	private Long id;
	/**
	 * 配置项类型
	 */
	private String typeCode;
	/**
	 * 配置项编码
	 */
	private String code;
	/**
	 * 配置值
	 */
	private String configValue;
	/**
	 * 是否加密**0-不加密 1-加密**
	 */
	@TableField(fill = FieldFill.INSERT)
	private Integer flagEncrypt;

}