package com.sansec.ai.common.service;

import com.sansec.ai.common.constant.IConfigCode;

import java.util.Map;

/**
 * 系统配置项
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
public interface SysConfigService {

	/**
	 * 获取配置值<br>
	 * 如果有多个，只返回一个
	 *
	 * @param code
	 * @return
	 */
	String getConfigValue(IConfigCode code);

	/**
	 * 获取配置项编码和配置值的集合
	 *
	 * @param code
	 * @return Map<配置项编码, 配置值>
	 */
	Map<String, String> getConfigValueMap(IConfigCode code);

}