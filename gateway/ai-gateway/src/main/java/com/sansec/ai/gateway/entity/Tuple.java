package com.sansec.ai.gateway.entity;

import java.io.Serializable;

/**
 * 自定义二元组，可以用于以下场景<br>
 * 1-用来作为本地缓存对象和过期时间
 * 2-用来存储任意格式的二元数组
 *
 * @param <K>
 * @param <V>
 */
public class Tuple<K, V> implements Serializable {

	private K key;

	private V value;

	public Tuple(K key, V value) {
		this.key = key;
		this.value = value;
	}

	public K getKey() {
		return key;
	}

	public V getValue() {
		return value;
	}

	public void setKey(K key) {
		this.key = key;
	}

	public void setValue(V value) {
		this.value = value;
	}
}
