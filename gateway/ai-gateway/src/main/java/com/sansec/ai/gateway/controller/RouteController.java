package com.sansec.ai.gateway.controller;

import com.sansec.ai.gateway.service.DynamicRouteService;
import jakarta.annotation.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/routes")
public class RouteController {

    @Resource
    private DynamicRouteService dynamicRouteService;

    @PostMapping("/refresh")
    public ResponseEntity<String> refreshRoutes() {
        dynamicRouteService.refreshRoutes();
        return ResponseEntity.ok("路由刷新成功");
    }
}