package com.sansec.ai.gateway.repository;

import com.sansec.ai.gateway.entity.UserWithPortalRelation;
import org.springframework.data.jpa.repository.JpaRepository;

public interface UserWithPortalRelationRepository extends JpaRepository<UserWithPortalRelation, Long> {

	/**
	 * 通过flagDel、portalId和userId查询匹配的记录数量
	 *
	 * @param flagDel  删除标志
	 * @param portalId 门户ID
	 * @param userId   用户ID
	 * @return 匹配的记录数量
	 */
	Long countByFlagDelAndPortalIdAndUserId(int flagDel, Long portalId, Long userId);

}