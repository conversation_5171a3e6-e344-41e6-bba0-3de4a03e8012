package com.sansec.ai.gateway.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Data
@Entity
@Table(name = "gateway_route")
public class CustomerRouteDefinition {
    @Id
    private Long id;

    @Column(nullable = false)
    private String routeId;

    @Column(nullable = false)
    private String uri;

    @Column(nullable = false)
    private String predicates;

    private String filters;

    @Column(nullable = false)
    private Integer orderNum;

    @Column(nullable = false)
    private Boolean enabled;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    private String remark;

    @Column(name = "create_by")
    private String createBy;

    @Column(name = "update_by")
    private String updateBy;

    @Column(name = "flag_del")
    private Integer flagDel;
} 