package com.sansec.ai.gateway.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.cloud.gateway.handler.predicate.PredicateDefinition;

import java.util.List;

public class Main {
    public static void main(String[] args) {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 示例 JSON 字符串
        String json = "[{\"name\":\"Path\",\"args\":{\"pattern\":\"/api/**\"}},{\"name\":\"Method\",\"args\":{\"_method\":\"GET\"}}]";

        PredicateDefinition p = new PredicateDefinition();
        try {
            List<PredicateDefinition> predicateDefinitions = objectMapper.readValue(
                    json,
                    new TypeReference<List<PredicateDefinition>>() {}
            );

            // 输出转换后的对象
            for (PredicateDefinition predicate : predicateDefinitions) {
                System.out.println(predicate);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}