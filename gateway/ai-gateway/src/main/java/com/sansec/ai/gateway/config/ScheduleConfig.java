package com.sansec.ai.gateway.config;

import com.sansec.ai.gateway.service.DynamicRouteService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;


@Slf4j
@Configuration
@EnableScheduling
public class ScheduleConfig {

	@Resource
	private DynamicRouteService dynamicRouteService;
	@Resource
	private DefaultRouteGlobalFilter defaultRouteGlobalFilter;


	@Scheduled(fixedRate = 5000) // 每60秒执行一次
	public void refreshRouteSchedule() {
//        log.info("开始刷新路由信息...");
		dynamicRouteService.refreshRoutes();
	}


	/**
	 * 凌晨1点清理缓存
	 */
	@Scheduled(cron = "0 0 1 * * ?")
	public void cleanExpiredCache() {
		defaultRouteGlobalFilter.cleanExpiredCache();
	}

} 