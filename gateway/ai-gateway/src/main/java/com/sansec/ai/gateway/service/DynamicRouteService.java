package com.sansec.ai.gateway.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sansec.ai.gateway.entity.CustomerRouteDefinition;
import com.sansec.ai.gateway.repository.RouteDefinitionRepository;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.event.RefreshRoutesEvent;
import org.springframework.cloud.gateway.filter.FilterDefinition;
import org.springframework.cloud.gateway.handler.predicate.PredicateDefinition;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.cloud.gateway.route.RouteDefinitionLocator;
import org.springframework.cloud.gateway.route.RouteDefinitionWriter;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.List;

@Slf4j
@Service
public class DynamicRouteService implements ApplicationEventPublisherAware {

    @Resource
    private RouteDefinitionWriter routeDefinitionWriter;

    @Resource
    private RouteDefinitionLocator routeDefinitionLocator;

    @Resource
    private RouteDefinitionRepository routeDefinitionRepository;

    @Resource
    private ObjectMapper objectMapper;

    private ApplicationEventPublisher publisher;

    @Value("${sansec.ai.default-route}")
    private String defaultRouteId;

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.publisher = applicationEventPublisher;
    }

    public void refreshRoutes() {
        List<CustomerRouteDefinition> customerRouteDefinitions = routeDefinitionRepository.findByFlagDel(0);
        deleteRoute(customerRouteDefinitions);
        customerRouteDefinitions.forEach(this::updateRoute);
        this.publisher.publishEvent(new RefreshRoutesEvent(this));
    }

    private void updateRoute(CustomerRouteDefinition customerRouteDefinition) {
        try {
            RouteDefinition definition = new RouteDefinition();
            definition.setId(customerRouteDefinition.getRouteId());
            definition.setUri(new URI(customerRouteDefinition.getUri()));
            definition.setOrder(customerRouteDefinition.getOrderNum());

            // 设置断言
            List<PredicateDefinition> predicateDefinitions = objectMapper.readValue(
                    customerRouteDefinition.getPredicates(),
                    new TypeReference<List<PredicateDefinition>>() {
                    }
            );
            definition.setPredicates(predicateDefinitions);

            // 设置过滤器
            if (customerRouteDefinition.getFilters() != null && !customerRouteDefinition.getFilters().isEmpty()) {
                List<FilterDefinition> filterDefinitions = objectMapper.readValue(
                        customerRouteDefinition.getFilters(),
                        new TypeReference<List<FilterDefinition>>() {
                        }
                );
                definition.setFilters(filterDefinitions);
            }

            routeDefinitionWriter.delete(Mono.just(definition.getId()));
            routeDefinitionWriter.save(Mono.just(definition)).subscribe();

            log.info("更新路由成功: {}", customerRouteDefinition.getRouteId());
        } catch (Exception e) {
            log.error("更新路由失败: " + customerRouteDefinition.getRouteId(), e);
        }
    }

    private void deleteRoute(List<CustomerRouteDefinition> customerRouteDefinitions) {
        List<String> dbRouteIdList = customerRouteDefinitions.stream().map(CustomerRouteDefinition::getRouteId).toList();
        List<RouteDefinition> routes = routeDefinitionLocator.getRouteDefinitions().collectList().block();
        if (routes.size() > 1) {
            routes.forEach(item -> {
                if (!item.getId().equals(defaultRouteId) && !dbRouteIdList.contains(item.getId())) routeDefinitionWriter.delete(Mono.just(item.getId())).block();
            });
        }
    }
} 