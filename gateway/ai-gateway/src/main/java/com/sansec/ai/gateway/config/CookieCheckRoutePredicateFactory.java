package com.sansec.ai.gateway.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.handler.predicate.AbstractRoutePredicateFactory;
import org.springframework.http.HttpCookie;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import java.util.Collections;
import java.util.function.Predicate;

@Component
public class CookieCheckRoutePredicateFactory extends AbstractRoutePredicateFactory<CookieCheckRoutePredicateFactory.Config> {

    @Value("${sansec.ai.cookie-name:difyCookie}")
    private String cookieName;


    public CookieCheckRoutePredicateFactory() {
        super(Config.class);
    }

    @Override
    public Predicate<ServerWebExchange> apply(Config config) {

        return exchange -> {
            String expectedValue = config.getExpectedValue();
            return exchange.getRequest().getCookies()
                    .getOrDefault(cookieName, Collections.emptyList())
                    .stream()
                    .map(HttpCookie::getValue)
                    .findFirst()
                    .filter(value -> value.equals(expectedValue))
                    .isPresent();
        };
    }

    public static class Config {
        private String expectedValue;

        public String getExpectedValue() {
            return expectedValue;
        }

        public void setExpectedValue(String expectedValue) {
            this.expectedValue = expectedValue;
        }
    }
}