package com.sansec.ai.gateway.repository;

import com.sansec.ai.gateway.entity.CustomerRouteDefinition;
import org.springframework.data.jpa.repository.JpaRepository;
import java.util.List;

public interface RouteDefinitionRepository extends JpaRepository<CustomerRouteDefinition, Long> {
    List<CustomerRouteDefinition> findByEnabledTrue();

    List<CustomerRouteDefinition> findByFlagDel(int flagDel);
} 