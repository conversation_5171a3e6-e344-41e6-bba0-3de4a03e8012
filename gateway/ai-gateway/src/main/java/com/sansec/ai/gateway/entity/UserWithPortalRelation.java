package com.sansec.ai.gateway.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.util.Date;

@Data
@Entity
@Table(name = "portal_user_relation")
public class UserWithPortalRelation {
    @Id
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "portal_id")
    private Long portalId;

    @Column(name = "user_type")
    private String userType;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    private String remark;

    @Column(name = "create_by")
    private String createBy;

    @Column(name = "update_by")
    private String updateBy;

    @Column(name = "flag_del")
    private Integer flagDel;
} 