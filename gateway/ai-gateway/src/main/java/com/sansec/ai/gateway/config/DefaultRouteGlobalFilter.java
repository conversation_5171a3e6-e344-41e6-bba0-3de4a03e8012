package com.sansec.ai.gateway.config;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import com.sansec.ai.gateway.entity.Tuple;
import com.sansec.ai.gateway.repository.UserWithPortalRelationRepository;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.core.Ordered;
import org.springframework.http.*;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class DefaultRouteGlobalFilter implements GlobalFilter, Ordered {

	@Resource
	private UserWithPortalRelationRepository userWithPortalRelationRepository;

	/**
	 * 缓存失效时长，1min
	 */
	private static final long FLAG_CACHE_INVALID_MS = 60 * 1000L;
	/**
	 * key为userId，value为Tuple<数据保存时间戳，关联的门户数量>
	 */
	private Map<Long, Tuple<Long, Long>> relationCacheMap = new ConcurrentHashMap<>();

	@Override
	public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
		// 如果请求未被任何路由匹配，Spring Cloud Gateway 会自动返回 404
		//获取当前路由
		Route route = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
		if (route == null) {
			//FIXME 该代码冗余：因为所有门户的路由地址都是/**，所以不存在找不到路由的情况
			return chain.filter(exchange);
		}

		// 获取请求路径
		String path = exchange.getRequest().getPath().value();
		// Dify管理端   如果是 /AiPortal/{portal_code}/**，则将 portal_code 写入 Cookie，并跳过 token 校验
		if (path.startsWith("/AiPortal/") && path.length() > "/AiPortal/".length()) {
			// 提取 portal_code
			String portalCode = path.substring("/AiPortal/".length());
			if (portalCode.contains("/")) {
				portalCode = portalCode.substring(0, portalCode.indexOf("/"));
			}

			// 构建 Set-Cookie 头
			ResponseCookie cookie = ResponseCookie.from("portal_code", portalCode)
					.path("/")
					.httpOnly(false) // 根据需要设置
					.build();

			exchange.getResponse().getHeaders().add(HttpHeaders.SET_COOKIE, cookie.toString());
			return chain.filter(exchange);
		}
		//门户端
		if (path.startsWith("/AiPortalC/") && path.length() > "/AiPortalC/".length()) {
			// 提取 portal_code
			String portalCode = path.substring("/AiPortalC/".length());
			if (portalCode.contains("/")) {
				portalCode = portalCode.substring(0, portalCode.indexOf("/"));
			}

			// 构建 Set-Cookie 头
			ResponseCookie cookie = ResponseCookie.from("portal_code", portalCode)
					.path("/")
					.httpOnly(false) // 根据需要设置
					.build();

			exchange.getResponse().getHeaders().add(HttpHeaders.SET_COOKIE, cookie.toString());
			return chain.filter(exchange);
		}


		//门户ID = routeId
		Long portalId = Long.parseLong(route.getId());

		//FIXME 遗留一个小漏洞：只要有门户管理员的token，就可以直接访问任意门户下的应用
		//NOTE 1-门户管理员ID
		Long userId = getUserIdFromToken(exchange.getRequest(), "SecToken");
		if (userId != null) {
			return chain.filter(exchange);
		}

		//NOTE 2-门户用户ID
		userId = getUserIdFromToken(exchange.getRequest(), "AiPortalToken");
		if(route.getOrder() == 99){
			return chain.filter(exchange);
		}
		if (userId != null && isMatching(userId, portalId)) {
			return chain.filter(exchange);
		}

		//NOTE 3-走到这里的都是权限校验失败的
		log.error("用户权限校验失败[userId={}, portalId={}, uri={}]", userId, portalId, route.getUri().toString());
		exchange.getResponse().setStatusCode(HttpStatus.BAD_GATEWAY);
		// 设置响应内容类型
		exchange.getResponse().getHeaders().setContentType(MediaType.APPLICATION_JSON);
		// 设置响应体
		String responseBody = "{\"code\": \"502\",\"error\": \"Bad Gateway\", \"message\": \"Current user does not has access to this portal\"}";
		return exchange.getResponse().writeWith(Mono.just(
				exchange.getResponse().bufferFactory().wrap(responseBody.getBytes())));
	}

	@Override
	public int getOrder() {
		// 确保在所有filter前面执行
		return Ordered.HIGHEST_PRECEDENCE;
	}

	/**
	 * 校验用户ID和门户ID是否匹配
	 *
	 * @param userId
	 * @param portalId
	 * @return
	 */
	private boolean isMatching(Long userId, Long portalId) {
		//缓存机制
		Tuple<Long, Long> relation = relationCacheMap.get(userId);
		if (relation == null || relation.getKey() < System.currentTimeMillis()) {
			//FIXME 高并发下会出现线程饥饿问题
			Long count = userWithPortalRelationRepository.countByFlagDelAndPortalIdAndUserId(0, portalId, userId);
			relation = new Tuple<>(System.currentTimeMillis() + FLAG_CACHE_INVALID_MS, count);
			relationCacheMap.put(userId, relation);
		}
		return relation.getValue() > 0;
	}

	/**
	 * 清除过期的缓存
	 */
	public void cleanExpiredCache() {
		long currentTime = System.currentTimeMillis();
		System.out.println("start to clean expired cache in DefaultRouteGlobalFilter.relationMap, size:" + relationCacheMap.size());
		// 使用removeIf方法一次性移除所有过期项，避免迭代器操作和显式移除
		relationCacheMap.entrySet().removeIf(entry -> entry.getValue().getKey() < currentTime);
		System.out.println("end to clean expired cache in DefaultRouteGlobalFilter.relationMap, size:" + relationCacheMap.size());
	}

	/**
	 * 从token中获取userId
	 *
	 * @param request
	 * @param tokenName
	 * @return
	 */
	private Long getUserIdFromToken(ServerHttpRequest request, String tokenName) {
		// 从cookie中获取AiPortalToken
		List<String> cookieValues = request.getCookies()
				.getOrDefault(tokenName, java.util.Collections.emptyList())
				.stream()
				.map(HttpCookie::getValue)
				.toList();
		if (cookieValues.isEmpty()) {
			System.err.println("no token is available in cookie");
			return null;
		}
		String token = cookieValues.get(0);
		if (StringUtils.isBlank(token)) {
			System.err.println("token is empty");
			return null;
		}
		JWT jwt = JWTUtil.parseToken(token);
		long userId = NumberUtil.parseLong(String.valueOf(jwt.getPayload().getClaim("userId")));
		if (userId == 0L) {
			System.err.println("no userId is available in token");
			return null;
		}
		return userId;
	}
}