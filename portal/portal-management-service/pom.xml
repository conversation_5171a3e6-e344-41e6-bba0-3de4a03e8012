<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.sansec.ai</groupId>
		<artifactId>ai-unified-portal</artifactId>
		<version>1.0-SNAPSHOT</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>

	<artifactId>portal-management-service</artifactId>
	<description>统一门户管理服务</description>

	<properties>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<sonar.projectKey>portal-management-service</sonar.projectKey>
		<sonar.projectName>portal-management-service</sonar.projectName>
	</properties>


	<dependencies>
		<dependency>
			<groupId>com.sansec.ai</groupId>
			<artifactId>common-base</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sansec.ai</groupId>
			<artifactId>portal-management-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sansec.ai</groupId>
			<artifactId>user-manager-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sansec.ai</groupId>
			<artifactId>user-auth-service</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sansec.ai</groupId>
			<artifactId>common-config</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sansec.ai</groupId>
			<artifactId>extra-dify-service</artifactId>
		</dependency>
	</dependencies>

	<build>
		<resources>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
				</includes>
				<filtering>true</filtering>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*</include>
				</includes>
				<filtering>true</filtering>
			</resource>
		</resources>
	</build>

</project>