<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sansec.ai.portal.mapper.PortalInfoMapper">
    <sql id="Base_Column_List" >
		id
		,portal_name,portal_code,portal_title,portal_logo,instance_type,instance_id,default_app_id,status,status_desc,flag_del,remark,create_by,create_time,update_by,update_time
	</sql>

	<select id="getPagedPortalList" resultType="com.sansec.ai.portal.entity.vo.PortalListVo">
		select
		portal.id,
		portal.portal_name,
		portal.portal_code,
		portal.portal_title,
		portal.portal_logo,
		portal.instance_id,
		portal.status,
		portal.status_desc,
		portal.create_time,
		u.id as userId,
		u.user_name as userName
		from portal_info portal
		left join portal_user_relation rel on rel.portal_id = portal.id and rel.user_type = 'manager'
		left join sec_user u on u.id = rel.user_id
		<where>
			portal.flag_del = 0
			<if test="param.portalId != null">
				and portal.id = #{param.portalId}
			</if>
			<if test="param.status != null">
				and portal.status = #{param.status}
			</if>
			<if test="param.portalName != null and param.portalName != ''">
				<bind name="portalName" value="'%'+param.portalName+'%'"/>
				and portal.portal_name LIKE #{portalName}
			</if>
			<if test="param.portalCode != null and param.portalCode != ''">
				and portal.portal_code = #{param.portalCode}
			</if>
			<if test="param.portalTitle != null and param.portalTitle != ''">
				and portal.portal_code = #{param.portalTitle}
			</if>
		</where>
		order by portal.create_time desc
	</select>

</mapper>