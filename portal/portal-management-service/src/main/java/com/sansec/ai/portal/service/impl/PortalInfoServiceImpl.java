package com.sansec.ai.portal.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ai.common.base.enums.TokenTypeEnum;
import com.sansec.ai.common.base.utils.TokenUtil;
import com.sansec.ai.common.service.SysConfigService;
import com.sansec.ai.extra.dify.constant.DifyConfigEnum;
import com.sansec.ai.portal.constant.PortalErrorCodeConst;
import com.sansec.ai.portal.constant.PortalStatusEnum;
import com.sansec.ai.portal.entity.convert.PortalInfoConvert;
import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.ai.portal.entity.request.PortalCreateRequest;
import com.sansec.ai.portal.entity.request.PortalListPagedRequest;
import com.sansec.ai.portal.entity.vo.PortalDetailVo;
import com.sansec.ai.portal.entity.vo.PortalListVo;
import com.sansec.ai.portal.entity.vo.PortalSiteInfoVo;
import com.sansec.ai.portal.listener.PortalCreateEvent;
import com.sansec.ai.portal.mapper.PortalInfoMapper;
import com.sansec.ai.portal.service.DifyInstanceInfoService;
import com.sansec.ai.portal.service.PortalInfoService;
import com.sansec.ai.user.entity.vo.PortalManagerVO;
import com.sansec.ai.user.service.IPortalManagerService;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.id.IdGenerator;
import com.sansec.common.param.response.SecPageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * 门户信息;
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
@Service
@Slf4j
public class PortalInfoServiceImpl extends ServiceImpl<PortalInfoMapper, PortalInfo> implements PortalInfoService, InitializingBean {

	@Autowired
	private PortalInfoConvert convert;
	@Autowired
	private SysConfigService sysConfigService;
	@Autowired
	private DifyInstanceInfoService difyInstanceInfoService;
	@Autowired
	private ApplicationContext context;
	@Autowired
	private IPortalManagerService portalManagerService;

	/**
	 * 默认应用ID
	 */
	private String defaultAppId;
	/**
	 * 默认应用名称
	 */
	private String defaultAppName;

	/**
	 * 在Bean初始化完成后执行，从系统配置中获取业务数据库和插件数据库模板名称
	 *
	 * @throws Exception
	 */
	@Override
	public void afterPropertiesSet() throws Exception {
		defaultAppId = sysConfigService.getConfigValue(DifyConfigEnum.DEFAULT_APP_ID);
		defaultAppName = sysConfigService.getConfigValue(DifyConfigEnum.DEFAULT_APP_NAME);
	}

	/**
	 * 获取门户网站配置
	 *
	 * @param portalId
	 * @return
	 */
	@Override
	public PortalSiteInfoVo getPortalSiteInfo(Long portalId) {
		PortalInfo portalInfo = this.getById(portalId);
		if (portalInfo == null) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_NOT_EXIST);
		}
		PortalSiteInfoVo siteInfo = convert.toSiteInfo(portalInfo);
		//补全默认应用
		siteInfo.setDefaultAppId(defaultAppId);
		siteInfo.setDefaultAppName(defaultAppName);
		return siteInfo;
	}

	/**
	 * 获取门户详情
	 *
	 * @param portalId
	 * @return
	 */
	@Override
	public PortalDetailVo getPortalDetail(Long portalId) throws BusinessException {
		PortalInfo portalInfo = getById(portalId);
		if (portalInfo == null) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_NOT_EXIST);
		}
		PortalDetailVo detail = convert.toPortalDetail(portalInfo);
		detail.setDefaultAppId(defaultAppId);
		detail.setDefaultAppName(defaultAppName);
		//补全Dify实例信息
		detail.setInstanceInfo(difyInstanceInfoService.getDifyInstanceInfoByPortalId(portalId));
		//补全管理员
		{
			PortalManagerVO manager = portalManagerService.getByPortalId(detail.getId());
			if (manager != null) {
				detail.setUserId(manager.getId());
				detail.setUserName(manager.getUserName());
			}
		}
		return detail;
	}

	/**
	 * 分页获取门户列表
	 *
	 * @param request
	 * @return
	 */
	@Override
	public SecPageVO<PortalListVo> getPagedPortalList(PortalListPagedRequest request) {
		// 分页查询
		IPage<PortalInfo> page = new Page<>(request.getPageNum(), request.getPageSize());
		IPage<PortalListVo> pagination = baseMapper.getPagedPortalList(page, request);

		SecPageVO<PortalListVo> pageList = convert.toPagedPortalList(pagination);
		//补全默认应用
		for (PortalListVo record : pageList.getList()) {
			record.setDefaultAppId(defaultAppId);
			record.setDefaultAppName(defaultAppName);
		}
		return pageList;
	}

	/**
	 * 创建门户
	 *
	 * @param request
	 * @return
	 * @throws BusinessException
	 */
	@Override
	public Long createPortal(PortalCreateRequest request) {
		//NOTE 1-保存门户信息
		PortalInfo entity = new PortalInfo();
		// ID
		entity.setId(IdGenerator.ins().generator());
		// 门户名称
		entity.setPortalName(request.getPortalName());
		// 门户编码/URL前缀/实例前缀
		entity.setPortalCode(request.getPortalCode());
		// 门户标题
		entity.setPortalTitle(request.getPortalTitle());
		// 门户logo，base64
		entity.setPortalLogo(request.getPortalLogo());
		// 实例类型**1-Dify**
		entity.setInstanceType(1);
		// 实例ID
		entity.setInstanceId(null);
		// 门户状态{@link com.sansec.ai.portal.constant.PortalStatusEnum}
		entity.setStatus(PortalStatusEnum.INITIALIZING.code);
		save(entity);

		//NOTE 3-异步部署实例
		context.publishEvent(new PortalCreateEvent(request, entity, TokenUtil.getTokenFromHeader(TokenTypeEnum.SEC_PLAT_TOKEN)));
		return 0L;
	}

}