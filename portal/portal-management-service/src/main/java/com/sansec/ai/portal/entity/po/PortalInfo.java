package com.sansec.ai.portal.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sansec.ai.common.base.entity.BaseEntity;
import lombok.Data;

/**
 * 门户信息;
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
@TableName("portal_info")
@Data
public class PortalInfo extends BaseEntity {
	/**
	 * ID
	 */
	@TableId(type = IdType.INPUT)
	private Long id;
	/**
	 * 门户名称
	 */
	private String portalName;
	/**
	 * 门户编码/URL前缀/实例前缀
	 */
	private String portalCode;
	/**
	 * 门户标题
	 */
	private String portalTitle;
	/**
	 * 门户logo，base64
	 */
	private String portalLogo;
	/**
	 * 实例类型**1-Dify**
	 */
	private Integer instanceType;
	/**
	 * 实例ID
	 */
	private Long instanceId;
	/**
	 * 门户状态{@link com.sansec.ai.portal.constant.PortalStatusEnum}
	 */
	private Integer status;
	/**
	 * 门户状态描述
	 */
	private String statusDesc;

}