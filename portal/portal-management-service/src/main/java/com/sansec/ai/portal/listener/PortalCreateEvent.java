package com.sansec.ai.portal.listener;

import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.ai.portal.entity.request.PortalCreateRequest;
import com.sansec.common.id.IdGenerator;
import org.springframework.context.ApplicationEvent;

/**
 * 创建门户事件
 *
 * <AUTHOR>
 * @since 2025/5/10 10:15
 */
public class PortalCreateEvent extends ApplicationEvent {

	/**
	 * 创建门户请求
	 */
	PortalCreateRequest request;

	/**
	 * 门户信息
	 */
	PortalInfo portalInfo;

	String secToken;

	public PortalCreateEvent(PortalCreateRequest request, PortalInfo portalInfo,String secToken) {
		super(IdGenerator.ins().generator());
		this.request = request;
		this.portalInfo = portalInfo;
		this.secToken = secToken;
	}
}
