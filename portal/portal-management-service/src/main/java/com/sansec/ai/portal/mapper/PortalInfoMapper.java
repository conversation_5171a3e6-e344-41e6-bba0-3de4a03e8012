package com.sansec.ai.portal.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.ai.portal.entity.request.PortalListPagedRequest;
import com.sansec.ai.portal.entity.vo.PortalListVo;
import com.sansec.db.mapper.SansecBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 门户信息;
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
@Mapper
public interface PortalInfoMapper extends SansecBaseMapper<PortalInfo> {

	/**
	 * 获取门户列表
	 *
	 * @param page
	 * @param request
	 * @return
	 */
	IPage<PortalListVo> getPagedPortalList(IPage<PortalInfo> page, @Param("param") PortalListPagedRequest request);

}