package com.sansec.ai.portal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sansec.ai.portal.entity.po.DifyInstanceInfo;
import com.sansec.ai.portal.entity.vo.DifyAccountInfo;
import com.sansec.ai.portal.entity.vo.DifyInstanceInfoVo;

/**
 * Dify实例信息;
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
public interface DifyInstanceInfoService extends IService<DifyInstanceInfo> {

	/**
	 * 根据门户ID，获取Dify实例信息
	 *
	 * @param portalId
	 * @return
	 */
	DifyInstanceInfoVo getDifyInstanceInfoByPortalId(Long portalId);


	DifyAccountInfo getDifyInstanceInfoByIpPort(String ip, int port);
}