package com.sansec.ai.portal.controller;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseCookie;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


/**
 * 门户信息控制器
 * 提供门户的创建、编辑、删除、查询等功能
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
@Slf4j
@RestController
public class RedirectController {

	/**
	 * 统一门户地址
	 */
	@Value("${ai.portal.url}")
	private String portalUrl;
	/**
	 * Dify实例管理端地址
	 */
	@Value("${ai.dify.url}")
	private String difyUrl;
	/**
	 * 统一门户Cookie
	 */
	@Value("${ai.portal.cookie-name}")
	private String cookieName;

	@PostMapping("/test/moderation")
	public Object test(@RequestBody JSONObject params) {
		String point = params.getString("point");
		switch (point) {
			case "ping":
				log.info("当前检查点[params={}]", params);
				return new JSONObject().fluentPut("result", "pong");
			case "app.moderation.input": {
				log.info("当前用户输入[params={}]", params);
				JSONObject jsonObject = new JSONObject();
				// 是否违反校验规则
				jsonObject.put("flagged", false);
				//动作，direct_output 直接输出预设回答; overridden 覆写传入变量值
				jsonObject.put("action", "direct_output");
				jsonObject.put("preset_response", "这是预设回答");

				// 终端用户传入变量值，key 为变量名，value 为变量值（仅当 action=overridden 返回）
				JSONObject inputs = new JSONObject();
				inputs.put("var_1", "value_1");
				inputs.put("var_2", "value_2");
				// 将 inputs 对象放入 jsonObject
				jsonObject.put("inputs", inputs);

				// 添加 query 字段
				jsonObject.put("query", "这是覆写的查询");
				return jsonObject;
			}
			case "app.moderation.output": {
				log.info("当前用户输出[params={}]", params);
				JSONObject jsonObject = new JSONObject();
				// mock: 是否违反校验规则
				if (RandomUtil.randomBoolean()) {
					// 违反校验规则
					jsonObject.put("flagged", true);
					if (RandomUtil.randomBoolean()) {
						//direct_output 直接输出预设回答
						jsonObject.put("action", "direct_output");
						jsonObject.put("preset_response", "这是预设回答");
					} else {
						//overridden 覆写传入变量值
						jsonObject.put("action", "overridden");
						jsonObject.put("text", "这是覆写后的内容" + params.getJSONObject("params").getString("text"));
					}
				} else {
					// 未违反校验规则
					jsonObject.put("flagged", false);
				}
				return jsonObject;
			}
			default:
				return null;
		}
	}

	/**
	 * 统一门户端重定向
	 *
	 * @param portalCode
	 * @param response
	 * @return
	 */
	@GetMapping("/portal/{portalCode}")
	public ResponseEntity<Void> portalRedirect(@PathVariable("portalCode") String portalCode, HttpServletResponse response) {
		//FIXME 1.验证portal_code
//		if (!isValidCode(code)) {
//			return ResponseEntity.badRequest().build();
//		}

		// 创建cookie，并添加Cookie到响应
		response.addHeader("Set-Cookie", createSecureCookie(portalCode).toString());

		// 重定向到前端地址
		return ResponseEntity.status(302)
				.header("Location", portalUrl)
				.build();
	}

	/**
	 * Dify实例管理端重定向
	 *
	 * @param portalCode
	 * @param response
	 * @return
	 */
	@GetMapping("/instance/{portalCode}")
	public ResponseEntity<Void> InstanceRedirect(@PathVariable("portalCode") String portalCode, HttpServletResponse response) {
		//FIXME 1.验证portal_code
//		if (!isValidCode(code)) {
//			return ResponseEntity.badRequest().build();
//		}

		// 创建cookie，并添加Cookie到响应
		response.addHeader("Set-Cookie", createSecureCookie(portalCode).toString());

		// 重定向到前端地址
		return ResponseEntity.status(302)
				.header("Location", difyUrl)
				.build();
	}

	private ResponseCookie createSecureCookie(String portalCode) {
		return ResponseCookie.from(cookieName, portalCode)
				.secure(true)
				.path("/")
				.maxAge(-1) // Session cookie
				.build();
	}

}