package com.sansec.ai.portal.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.ai.portal.entity.request.PortalCreateRequest;
import com.sansec.ai.portal.entity.request.PortalListPagedRequest;
import com.sansec.ai.portal.entity.vo.PortalDetailVo;
import com.sansec.ai.portal.entity.vo.PortalListVo;
import com.sansec.ai.portal.entity.vo.PortalSiteInfoVo;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecPageVO;

/**
 * 门户信息;
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
public interface PortalInfoService extends IService<PortalInfo> {

	/**
	 * 获取门户网站配置
	 *
	 * @param portalId
	 * @return
	 */
	PortalSiteInfoVo getPortalSiteInfo(Long portalId);

	/**
	 * 获取门户详情
	 *
	 * @param portalId
	 * @return
	 */
	PortalDetailVo getPortalDetail(Long portalId) throws BusinessException;

	/**
	 * 分页获取门户列表
	 *
	 * @param request
	 * @return
	 */
	SecPageVO<PortalListVo> getPagedPortalList(PortalListPagedRequest request);

	/**
	 * 创建门户
	 *
	 * @param request
	 * @return
	 * @throws BusinessException
	 */
	Long createPortal(PortalCreateRequest request);

}