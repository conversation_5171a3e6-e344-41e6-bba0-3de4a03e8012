package com.sansec.ai.portal.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sansec.ai.auth.controller.BaseController;
import com.sansec.ai.common.base.constant.ErrorCode;
import com.sansec.ai.common.base.entity.BaseEntity;
import com.sansec.ai.common.base.enums.EnumDelFlag;
import com.sansec.ai.extra.dify.service.DifyDeployService;
import com.sansec.ai.portal.constant.PortalErrorCodeConst;
import com.sansec.ai.portal.constant.PortalStatusEnum;
import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.ai.portal.entity.request.PortalCreateRequest;
import com.sansec.ai.portal.entity.request.PortalListPagedRequest;
import com.sansec.ai.portal.entity.request.PortalUpdateRequest;
import com.sansec.ai.portal.entity.vo.PortalDetailVo;
import com.sansec.ai.portal.entity.vo.PortalListVo;
import com.sansec.ai.portal.listener.PortalDeleteEvent;
import com.sansec.ai.portal.listener.PortalStartEvent;
import com.sansec.ai.portal.listener.PortalStopEvent;
import com.sansec.ai.portal.service.PortalInfoService;
import com.sansec.ai.user.constant.UserTypeEnum;
import com.sansec.ai.user.service.IPortalManagerService;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecPageVO;
import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import com.sansec.common.utils.SM2Util;
import com.sansec.plat.base.utils.TokenUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Base64;
import java.util.Date;

/**
 * 门户信息控制器
 * 提供门户的创建、编辑、删除、查询等功能
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
@Slf4j
@RestController
@RequestMapping("/manager/portal")
@Validated
public class PortalInfoController extends BaseController {
	@Autowired
	private PortalInfoService portalInfoService;
	@Autowired
	private IPortalManagerService portalManagerService;
	@Autowired
	private ApplicationContext context;
	@Autowired
	private DifyDeployService difyDeployService;

	/**
	 * 统一门户重定向地址
	 */
	@Value("${ai.portal.redirect-url}")
	private String portalRedirectUrl;
	/**
	 * Dify实例管理端重定向地址
	 */
	@Value("${ai.dify.redirect-url}")
	private String difyRedirectUrl;
	/**
	 * 私钥
	 */
	@Value("${ai.secret.private-key}")
	private String privateKey;
	/**
	 * 最大门户数量
	 */
	@Value("${ai.portal.max-count:8}")
	private Integer portalMaxCount;

	/**
	 * 创建门户
	 * 创建新的门户信息，并异步初始化门户实例
	 *
	 * @param request 创建门户请求参数
	 * @return 新创建的门户ID
	 */
	@PostMapping("/add")
	public SecRestResponse<Long> add(@RequestBody PortalCreateRequest request) {
		log.info("创建门户请求[request={}]", request);
		//NOTE 1-校验
		//校验当前用户是否有权限创建门户
		if (UserTypeEnum.SYSTEM_MANAGER != getUserType()) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_CREATE_FAILED_S, true, "权限不足");
		}

		//校验门户名称是否重复
		long count = portalInfoService.count(new LambdaQueryWrapper<PortalInfo>()
				.eq(BaseEntity::getFlagDel, EnumDelFlag.NORMAL.getKey())
				.eq(PortalInfo::getPortalName, request.getPortalName()));
		if (count > 0) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_CREATE_FAILED_S, true, "门户名称重复");
		}
		//校验门户编码是否重复
		count = portalInfoService.count(new LambdaQueryWrapper<PortalInfo>()
				.eq(BaseEntity::getFlagDel, EnumDelFlag.NORMAL.getKey())
				.eq(PortalInfo::getPortalCode, request.getPortalCode()));
		if (count > 0) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_CREATE_FAILED_S, true, "门户编码重复");
		}
		//校验门户数量是否超限
		count = portalInfoService.count(new LambdaQueryWrapper<PortalInfo>()
				.eq(BaseEntity::getFlagDel, EnumDelFlag.NORMAL.getKey()));
		if (count >= portalMaxCount) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_MAX_COUNT_LIMITED);
		}
		//如果是新建管理员帐号，需要校验门户管理员账号是否已存在
		if (request.getUserId() == null) {
			boolean exist = portalManagerService.checkManagerExist(request.getUserAccount());
			if (exist) {
				throw new BusinessException(PortalErrorCodeConst.PORTAL_CREATE_FAILED_S, true, "门户管理员账号已存在");
			}
		}
		//密码解密
		{
			String decrypt = SM2Util.decrypt(privateKey, request.getUserPassword());
			if (decrypt == null) {
				throw new BusinessException(ErrorCode.PARAM_VERIFY_FAILED, true, "参数校验失败");
			}
			request.setUserPassword(new String(Base64.getDecoder().decode(decrypt)));
		}
		Long portalId = portalInfoService.createPortal(request);
		return ResultUtil.ok(portalId);
	}

	/**
	 * 更新门户信息
	 * 更新现有门户的名称、标题、logo等信息
	 *
	 * @param request 更新门户请求参数
	 * @return 更新的门户ID
	 */
	@PostMapping("/edit")
	public SecRestResponse<Long> edit(@RequestBody @Validated PortalUpdateRequest request) {
		log.info("更新门户请求: {}", request);
		// 检查门户是否存在
		PortalInfo portalInfo = portalInfoService.getById(request.getId());
		if (portalInfo == null) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_NOT_EXIST);
		}

		// 创建更新对象
		PortalInfo update = new PortalInfo();
		// 设置门户ID
		update.setId(request.getId());

		// 更新门户名称（如果提供了新的名称）
		if (StringUtils.isNotBlank(request.getPortalName())) {
			update.setPortalName(request.getPortalName());
		}

		// 更新门户标题（如果提供了新的标题）
		if (StringUtils.isNotBlank(request.getPortalTitle())) {
			update.setPortalTitle(request.getPortalTitle());
		}

		// 更新门户logo（如果提供了新的logo）
		if (StringUtils.isNotBlank(request.getPortalLogo())) {
			update.setPortalLogo(request.getPortalLogo());
		}

		portalInfoService.updateById(update);
		return ResultUtil.ok(request.getId());
	}

	/**
	 * 删除门户
	 * 删除指定的门户及其相关数据
	 *
	 * @param request 包含门户ID的请求参数
	 * @return 删除结果
	 */
	@PostMapping("/delete")
	public SecRestResponse<Object> delete(@RequestBody JSONObject request) {
		Long id = request.getLong("id");
		if (id == null) {
			return ResultUtil.error(ErrorCode.PARAM_VERIFY_FAILED);
		}
		// 检查门户是否存在
		PortalInfo portalInfo = portalInfoService.getById(id);
		if (portalInfo == null) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_NOT_EXIST);
		}
		// 检查门户状态是否支持删除
		PortalStatusEnum status = PortalStatusEnum.getByCode(portalInfo.getStatus());
		switch (status) {
			case INITIAL_ERROR:
			case STOPPED:
			case DELETE_ERROR:
				break;
			default:
				return ResultUtil.error(PortalErrorCodeConst.PORTAL_STATUS_NOT_SUPPORT);
		}
		//更新门户状态为删除中
		portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
				.eq(PortalInfo::getId, portalInfo.getId())
				.set(PortalInfo::getStatus, PortalStatusEnum.DELETING.code)
				.set(BaseEntity::getUpdateTime, new Date()));
		//异步启动
		context.publishEvent(new PortalDeleteEvent(portalInfo, TokenUtil.getTokenFromHeader()));
		return ResultUtil.ok("正在删除门户，请稍后");
	}

	/**
	 * 获取门户详情
	 * 获取指定门户的详细信息
	 *
	 * @param request 包含门户ID的请求参数
	 * @return 门户详情信息
	 */
	@PostMapping("/detail")
	public SecRestResponse<PortalDetailVo> detail(@RequestBody JSONObject request) {
		Long id = request.getLong("id");
		if (id == null) {
			return ResultUtil.error(ErrorCode.PARAM_VERIFY_FAILED);
		}

		PortalDetailVo detail = portalInfoService.getPortalDetail(id);
		if (detail == null) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_NOT_EXIST);
		}
		return ResultUtil.ok(detail);
	}

	/**
	 * 分页查询门户列表
	 * 根据条件分页查询门户列表
	 *
	 * @param request 查询条件及分页参数
	 * @return 门户列表分页数据
	 */
	@PostMapping("/pageList")
	public SecRestResponse<SecPageVO<PortalListVo>> pageList(@RequestBody PortalListPagedRequest request) {
		if (getUserType() == UserTypeEnum.PORTAL_MANAGER) {
			//门户管理员只能查询自己的门户
			Long portalId = getPortalId();
			request.setPortalId(portalId == null ? -1L : portalId);
		}
		SecPageVO<PortalListVo> pagination = portalInfoService.getPagedPortalList(request);
		return ResultUtil.ok(pagination);
	}

	/**
	 * 启动门户
	 * 启动指定的门户及其对应的Dify实例
	 *
	 * @param request 包含门户ID的请求参数
	 * @return 启动结果
	 */
	@PostMapping("/cmd/start")
	public SecRestResponse<Object> portalStart(@RequestBody JSONObject request) {
		Long id = request.getLong("id");
		if (id == null) {
			return ResultUtil.error(ErrorCode.PARAM_VERIFY_FAILED);
		}
		// 检查门户是否存在
		PortalInfo portalInfo = portalInfoService.getById(id);
		if (portalInfo == null) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_NOT_EXIST);
		}
		// 检查门户状态是否支持启动
		if (portalInfo.getStatus() != PortalStatusEnum.STOPPED.code) {
			return ResultUtil.error(PortalErrorCodeConst.PORTAL_STATUS_NOT_SUPPORT);
		}
		//更新门户状态为启动中
		portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
				.eq(PortalInfo::getId, portalInfo.getId())
				.set(PortalInfo::getStatus, PortalStatusEnum.STARTING.code)
				.set(PortalInfo::getStatusDesc, PortalStatusEnum.STARTING.description)
				.set(BaseEntity::getUpdateTime, new Date()));
		//异步启动
		context.publishEvent(new PortalStartEvent(portalInfo));
		return ResultUtil.ok("正在启动门户，请稍后");
	}

	/**
	 * 停止门户
	 * 停止指定的门户及其对应的Dify实例
	 *
	 * @param request 包含门户ID的请求参数
	 * @return 停止结果
	 */
	@PostMapping("/cmd/stop")
	public SecRestResponse<Object> portalStop(@RequestBody JSONObject request) throws InterruptedException {
		Long id = request.getLong("id");
		if (id == null) {
			return ResultUtil.error(ErrorCode.PARAM_VERIFY_FAILED);
		}
		// 检查门户是否存在
		PortalInfo portalInfo = portalInfoService.getById(id);
		if (portalInfo == null) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_NOT_EXIST);
		}
		// 检查门户状态是否支持停止
		PortalStatusEnum status = PortalStatusEnum.getByCode(portalInfo.getStatus());
		switch (status) {
			case RUNNING:
			case ABNORMAL:
				break;
			default:
				return ResultUtil.error(PortalErrorCodeConst.PORTAL_STATUS_NOT_SUPPORT);
		}
		//更新门户状态为停止中
		portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
				.eq(PortalInfo::getId, portalInfo.getId())
				.set(PortalInfo::getStatus, PortalStatusEnum.STOPPING.code)
				.set(PortalInfo::getStatusDesc, "")
				.set(BaseEntity::getUpdateTime, new Date()));
		//异步启动
		context.publishEvent(new PortalStopEvent(portalInfo));
		return ResultUtil.ok("正在停止门户，请稍后");
	}

	/**
	 * 获取门户首页URL
	 * 获取指定门户的首页访问URL
	 *
	 * @param request 包含门户ID的请求参数
	 * @return 门户首页URL
	 */
	@PostMapping("/getPortalUrl")
	public SecRestResponse<String> getPortalUrl(@RequestBody JSONObject request) {
		Long id = request.getLong("id");
		if (id == null) {
			return ResultUtil.ok(portalRedirectUrl.replace("%s", ""));
		}

		PortalInfo portalInfo = portalInfoService.getById(id);
		if (portalInfo == null) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_NOT_EXIST);
		}
		return ResultUtil.ok(String.format(portalRedirectUrl, portalInfo.getPortalCode()));
	}

	/**
	 * 获取门户管理端URL
	 * 获取指定门户的管理端访问URL
	 *
	 * @param request 包含门户ID的请求参数
	 * @return 门户管理端URL
	 */
	@PostMapping("/getConsoleUrl")
	public SecRestResponse<String> getConsoleUrl(@RequestBody JSONObject request) {
		Long id = request.getLong("id");
		if (id == null) {
			return ResultUtil.error(ErrorCode.PARAM_VERIFY_FAILED);
		}

		PortalInfo portalInfo = portalInfoService.getById(id);
		if (portalInfo == null) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_NOT_EXIST);
		}

		return ResultUtil.ok(String.format(difyRedirectUrl, portalInfo.getPortalCode()));
	}


}