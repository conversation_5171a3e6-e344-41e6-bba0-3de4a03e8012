package com.sansec.ai.portal.listener;

import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.common.id.IdGenerator;
import org.springframework.context.ApplicationEvent;

/**
 * 启动门户事件
 *
 * <AUTHOR>
 * @since 2025/5/10 10:15
 */
public class PortalDeleteEvent extends ApplicationEvent {

	/**
	 * 门户信息
	 */
	PortalInfo portalInfo;

	String secToken;

	public PortalDeleteEvent(PortalInfo portalInfo,String secToken) {
		super(IdGenerator.ins().generator());
		this.portalInfo = portalInfo;
		this.secToken = secToken;
	}
}
