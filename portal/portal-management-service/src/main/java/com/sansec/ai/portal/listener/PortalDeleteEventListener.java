package com.sansec.ai.portal.listener;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sansec.ai.common.base.entity.BaseEntity;
import com.sansec.ai.common.base.enums.EnumDelFlag;
import com.sansec.ai.extra.dify.service.DifyDeployService;
import com.sansec.ai.portal.constant.PortalStatusEnum;
import com.sansec.ai.portal.entity.po.DifyInstanceInfo;
import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.ai.portal.service.DifyInstanceInfoService;
import com.sansec.ai.portal.service.PortalInfoService;
import com.sansec.ai.user.entity.request.PortalManagerDeleteDTO;
import com.sansec.ai.user.service.IGatewayService;
import com.sansec.ai.user.service.IPortalManagerService;
import com.sansec.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 启动门户事件
 *
 * <AUTHOR>
 * @since 2025/5/10 10:16
 */
@Slf4j
@Component
public class PortalDeleteEventListener implements ApplicationListener<PortalDeleteEvent> {


	@Autowired
	private PortalInfoService portalInfoService;
	@Autowired
	private DifyInstanceInfoService difyInstanceInfoService;
	@Autowired
	private DifyDeployService difyDeployService;
	@Autowired
	private IPortalManagerService portalManagerService;
	@Autowired
	private IGatewayService gatewayService;

	@Async
	@Override
	public void onApplicationEvent(PortalDeleteEvent event) {
		log.info("异步删除Dify实例-开始[eventId={}]", event.getSource());

		try {
			//移除容器
			difyDeployService.removeInstance(event.portalInfo.getPortalCode());
		} catch (Exception e) {
			log.error(String.format("异步删除Dify实例-报错[eventId=%s]", event.getSource()), e);
			updatePortalOfError(event, e instanceof BusinessException ? e.getLocalizedMessage() : "移除容器失败");
			return;
		}
		try {
			//删除实例在磁盘上的所有数据
			difyDeployService.deleteAllInfo(event.portalInfo.getPortalCode());
		} catch (Exception e) {
			log.error(String.format("异步删除Dify实例-报错[eventId=%s]", event.getSource()), e);
			updatePortalOfError(event, e instanceof BusinessException ? e.getLocalizedMessage() : "删除门户下的数据失败");
			return;
		}

		//删除实例在数据库中的信息
		portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
				.eq(PortalInfo::getId, event.portalInfo.getId())
				.set(PortalInfo::getFlagDel, EnumDelFlag.DELETED.getKey())
				.set(BaseEntity::getUpdateTime, new Date()));
		difyInstanceInfoService.update(new LambdaUpdateWrapper<DifyInstanceInfo>()
				.eq(DifyInstanceInfo::getPortalId, event.portalInfo.getId())
				.set(DifyInstanceInfo::getFlagDel, EnumDelFlag.DELETED.getKey())
				.set(BaseEntity::getUpdateTime, new Date()));
		//删除管理员账号
		portalManagerService.delete(new PortalManagerDeleteDTO(event.portalInfo.getId(), true, event.secToken));

		//删除路由
		gatewayService.delete(String.valueOf(event.portalInfo.getId()));
		log.info(String.format("异步删除Dify实例-结束[eventId=%s]", event.getSource()));

	}

	/**
	 * 初始化失败，更新门户状态
	 *
	 * @param event
	 * @param message
	 */
	private void updatePortalOfError(PortalDeleteEvent event, String message) {
		portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
				.eq(PortalInfo::getId, event.portalInfo.getId())
				.set(PortalInfo::getStatus, PortalStatusEnum.DELETE_ERROR.code)
				.set(PortalInfo::getStatusDesc, message)
				.set(BaseEntity::getUpdateTime, new Date()));
	}

}
