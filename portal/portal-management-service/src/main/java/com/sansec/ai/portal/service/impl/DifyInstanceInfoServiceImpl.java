package com.sansec.ai.portal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sansec.ai.common.base.enums.EnumDelFlag;
import com.sansec.ai.portal.constant.PortalErrorCodeConst;
import com.sansec.ai.portal.entity.convert.DifyInstanceInfoConvert;
import com.sansec.ai.portal.entity.po.DifyInstanceInfo;
import com.sansec.ai.portal.entity.vo.DifyAccountInfo;
import com.sansec.ai.portal.entity.vo.DifyInstanceInfoVo;
import com.sansec.ai.portal.mapper.DifyInstanceInfoMapper;
import com.sansec.ai.portal.service.DifyInstanceInfoService;
import com.sansec.common.exception.BusinessException;
import com.sansec.component.algorithm.utill.ComponentSynthesisEncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Dify实例信息;
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
@Service
@Slf4j
public class DifyInstanceInfoServiceImpl extends ServiceImpl<DifyInstanceInfoMapper, DifyInstanceInfo> implements DifyInstanceInfoService {

	@Autowired
	private DifyInstanceInfoConvert convert;

	/**
	 * 根据门户ID，获取Dify实例信息
	 *
	 * @param portalId
	 * @return
	 */
	@Override
	public DifyInstanceInfoVo getDifyInstanceInfoByPortalId(Long portalId) {
		DifyInstanceInfo entity = getOne(new LambdaQueryWrapper<DifyInstanceInfo>()
				.eq(DifyInstanceInfo::getPortalId, portalId)
				.last("LIMIT 1"));
		if (entity == null) {
			throw new BusinessException(PortalErrorCodeConst.DIFY_INSTANCE_NOT_EXIST);
		}
		return convert.convertVo(entity);
	}

	@Override
	public DifyAccountInfo getDifyInstanceInfoByIpPort(String ip, int port) {
		LambdaQueryWrapper<DifyInstanceInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
		lambdaQueryWrapper.eq(DifyInstanceInfo::getInstanceIp, ip);
		lambdaQueryWrapper.eq(DifyInstanceInfo::getInstancePort, port);
		lambdaQueryWrapper.eq(DifyInstanceInfo::getFlagDel, EnumDelFlag.NORMAL.getKey());
		DifyInstanceInfo entity = getOne(lambdaQueryWrapper);

		if (entity == null) {
			log.error("Failed to retrieve Dify instance info for IP: {}, port: {}", ip, port);
			throw new BusinessException(PortalErrorCodeConst.DIFY_INSTANCE_NOT_EXIST);
		}

		DifyAccountInfo difyAccountInfo = new DifyAccountInfo();
		difyAccountInfo.setUsername(entity.getUserAccount());
		difyAccountInfo.setDecPwd(ComponentSynthesisEncryptionUtil.decPwd(entity.getUserPassword()));

		return difyAccountInfo;
	}
}