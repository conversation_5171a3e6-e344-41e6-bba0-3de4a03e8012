package com.sansec.ai.portal.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sansec.ai.common.base.entity.BaseEntity;
import com.sansec.ai.common.base.enums.EnumDelFlag;
import com.sansec.ai.extra.dify.result.DifyInstanceStatusVo;
import com.sansec.ai.extra.dify.service.DifyDeployService;
import com.sansec.ai.portal.constant.PortalStatusEnum;
import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.ai.portal.service.PortalInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 当应用程序启动时，检查门户状态
 *
 * <AUTHOR>
 * @since 2025/6/24 20:17
 */
@Slf4j
@Component
public class PortalCheckOnApplicationStart implements CommandLineRunner {

	@Autowired
	private PortalInfoService portalInfoService;
	@Autowired
	private DifyDeployService difyDeployService;

	@Override
	public void run(String... args) throws Exception {
		log.info("当应用程序启动时检查门户状态-开始");
		long currentTime = System.currentTimeMillis();
		// NOTE 1-查询所有未删除且处于以下状态的门户列表：异常、运行中
		List<PortalInfo> portalList = portalInfoService.list(new LambdaQueryWrapper<PortalInfo>()
				.eq(BaseEntity::getFlagDel, EnumDelFlag.NORMAL.getKey())
				.in(PortalInfo::getStatus, PortalStatusEnum.ABNORMAL.code, PortalStatusEnum.RUNNING.code));

		if (portalList.isEmpty()) {
			log.info("所有门户状态正常");
			return;
		}
		log.info("找到{}个需要重置状态的门户", portalList.size());
		// NOTE 2-分别重置每个门户的最新状态
		for (PortalInfo portal : portalList) {
			//NOTE 2.1-查询门户最新的状态
			DifyInstanceStatusVo health = difyDeployService.checkInstanceStatus(portal.getPortalCode());
			if (health.getHealth()) {
				log.error("当前门户状态正常，无需重置[portalCode={}]", portal.getPortalCode());
				continue;
			}
			//NOTE 2-重新启动门户
			log.error("当前门户状态异常，正在重置[portalCode={}]", portal.getPortalCode());
			try {
				log.info("正在启动Dify实例[portalCode={}]", portal.getPortalCode());
				difyDeployService.startInstance(portal.getPortalCode());
			} catch (Exception e) {
				log.error(String.format("启动Dify实例-报错[portalCode=%s]", portal.getPortalCode()), e);
				return;
			}
			//更新门户状态
			checkPortalOfSuccess(portal);
		}
		log.info("当应用程序启动时检查门户状态-结束[cost={}ms]", System.currentTimeMillis() - currentTime);
	}

	/**
	 * 初始化成功，检查门户状态
	 *
	 * @param portalInfo
	 */
	private void checkPortalOfSuccess(PortalInfo portalInfo) {
		boolean isHealthy = false;
		for (int i = 0; i < 3; i++) {
			try {
				Thread.sleep(10000L);
			} catch (Exception e) {
				log.error("检查门户状态[portalCode={}]", portalInfo.getPortalCode(), e);
			}
			DifyInstanceStatusVo statusVo = difyDeployService.checkInstanceStatus(portalInfo.getPortalCode());
			if (!statusVo.getHealth()) {
				log.error("当前门户状态检查异常，等待重试[portalCode={}]", portalInfo.getPortalCode());
				continue;
			}
			isHealthy = true;
			break;
		}
		if (isHealthy) {
			portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
					.eq(PortalInfo::getId, portalInfo.getId())
					.set(PortalInfo::getStatus, PortalStatusEnum.RUNNING.code)
					.set(PortalInfo::getStatusDesc, " ")
					.set(BaseEntity::getUpdateTime, new Date()));
		} else {
			portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
					.eq(PortalInfo::getId, portalInfo.getId())
					.set(PortalInfo::getStatus, PortalStatusEnum.ABNORMAL.code)
					.set(PortalInfo::getStatusDesc, "当前门户状态检查异常")
					.set(BaseEntity::getUpdateTime, new Date()));
		}
	}

}
