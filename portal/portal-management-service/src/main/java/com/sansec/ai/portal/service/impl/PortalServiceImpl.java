package com.sansec.ai.portal.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sansec.ai.common.base.entity.BaseEntity;
import com.sansec.ai.common.base.enums.EnumDelFlag;
import com.sansec.ai.portal.constant.PortalErrorCodeConst;
import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.ai.portal.entity.vo.DifyAccountInfo;
import com.sansec.ai.portal.entity.vo.DifyInstanceInfoVo;
import com.sansec.ai.portal.entity.vo.PortalSiteInfoVo;
import com.sansec.ai.portal.service.DifyInstanceInfoService;
import com.sansec.ai.portal.service.IPortalService;
import com.sansec.ai.portal.service.PortalInfoService;
import com.sansec.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/5/9 16:48
 */
@Slf4j
@Service
public class PortalServiceImpl implements IPortalService {

	@Autowired
	private PortalInfoService portalInfoService;
	@Autowired
	private DifyInstanceInfoService difyInstanceInfoService;

	/**
	 * 获取门户网站配置
	 *
	 * @param portalId
	 * @return
	 */
	@Override
	public PortalSiteInfoVo getPortalSiteInfo(Long portalId) {
		return portalInfoService.getPortalSiteInfo(portalId);
	}

	/**
	 * 获取门户网站配置
	 *
	 * @param portalCode
	 * @return
	 */
	@Override
	public PortalSiteInfoVo getPortalSiteInfo(String portalCode) {
		PortalInfo portalInfo = portalInfoService.getOne(new LambdaQueryWrapper<PortalInfo>().select(PortalInfo::getId)
				.eq(PortalInfo::getPortalCode, portalCode));
		if (portalInfo == null) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_NOT_EXIST);
		}
		return portalInfoService.getPortalSiteInfo(portalInfo.getId());
	}

	/**
	 * 根据门户ID，获取Dify实例信息
	 *
	 * @param portalId
	 * @return
	 */
	@Override
	public DifyInstanceInfoVo getDifyInstanceInfoByPortalId(Long portalId) {
		return difyInstanceInfoService.getDifyInstanceInfoByPortalId(portalId);
	}

	/**
	 * 根据门户前缀，获取Dify实例信息
	 *
	 * @param portalCode
	 * @return
	 */
	@Override
	public DifyInstanceInfoVo getDifyInstanceInfoByPortalCode(String portalCode) {
		PortalInfo portalInfo = portalInfoService.getOne(new LambdaQueryWrapper<PortalInfo>()
				.eq(PortalInfo::getPortalCode, portalCode)
				.eq(BaseEntity::getFlagDel, EnumDelFlag.NORMAL.getKey()), false);
		if (portalInfo == null) {
			throw new BusinessException(PortalErrorCodeConst.PORTAL_NOT_EXIST);
		}
		return getDifyInstanceInfoByPortalId(portalInfo.getId());
	}

	@Override
	public DifyAccountInfo getDifyInstanceInfoByIpPort(String ip, int port) {
		return difyInstanceInfoService.getDifyInstanceInfoByIpPort(ip, port);
	}
}
