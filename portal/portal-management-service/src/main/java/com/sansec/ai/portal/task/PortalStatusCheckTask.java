package com.sansec.ai.portal.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sansec.ai.common.base.entity.BaseEntity;
import com.sansec.ai.common.base.enums.EnumDelFlag;
import com.sansec.ai.extra.dify.result.DifyInstanceStatusVo;
import com.sansec.ai.extra.dify.service.DifyDeployService;
import com.sansec.ai.portal.constant.PortalStatusEnum;
import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.ai.portal.service.PortalInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 门户状态检查
 *
 * <AUTHOR>
 * @since 2025/5/29 19:24
 */
@Slf4j
@Component
public class PortalStatusCheckTask {

	@Autowired
	private PortalInfoService portalInfoService;

	@Autowired
	private DifyDeployService difyDeployService;

	/**
	 * 定时检查门户状态
	 * 每10秒执行一次，检查所有运行中或异常状态的门户实例状态
	 */
	@Scheduled(initialDelay = 10 * 1000L, fixedDelay = 10 * 1000L)
	public void run() {
		log.info("定时检查门户状态-开始");
		long currentTime = System.currentTimeMillis();
		// 1. 查询所有未删除且状态为运行中或异常的门户列表
		List<PortalInfo> portalList = portalInfoService.list(new LambdaQueryWrapper<PortalInfo>()
				.eq(BaseEntity::getFlagDel, EnumDelFlag.NORMAL.getKey())
				.in(PortalInfo::getStatus, PortalStatusEnum.RUNNING.code, PortalStatusEnum.ABNORMAL.code));

		if (portalList.isEmpty()) {
			log.info("没有需要检查状态的门户");
			return;
		}
		log.info("找到{}个需要检查状态的门户", portalList.size());

		// 2. 分别查询每个门户的最新状态
		for (PortalInfo portal : portalList) {
			try {
				// 通过difyDeployService检查实例状态
				DifyInstanceStatusVo statusVo = difyDeployService.checkInstanceStatus(portal.getPortalCode());
				// 3. 根据检查结果更新门户状态
				if (statusVo.getHealth()) {
					// 实例状态正常，更新为运行中
					portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
							.eq(PortalInfo::getId, portal.getId())
							.set(PortalInfo::getStatus, PortalStatusEnum.RUNNING.code)
							.set(PortalInfo::getStatusDesc, PortalStatusEnum.RUNNING.description)
							.set(BaseEntity::getUpdateTime, new Date()));
				} else {
					// 实例状态异常，更新为异常状态
					portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
							.eq(PortalInfo::getId, portal.getId())
							.set(PortalInfo::getStatus, PortalStatusEnum.ABNORMAL.code)
							.set(PortalInfo::getStatusDesc, statusVo.getMessage())
							.set(BaseEntity::getUpdateTime, new Date()));
				}

			} catch (Exception e) {
				log.error("检查门户状态异常[portalId={}, portalCode={}]", portal.getId(), portal.getPortalCode(), e);
				// 更新为异常状态
				portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
						.eq(PortalInfo::getId, portal.getId())
						.set(PortalInfo::getStatus, PortalStatusEnum.ABNORMAL.code)
						.set(PortalInfo::getStatusDesc, "执行状态检查任务异常")
						.set(BaseEntity::getUpdateTime, new Date()));
			}
		}
		log.info("定时检查门户状态-结束[cost={}ms]", System.currentTimeMillis() - currentTime);
	}


}
