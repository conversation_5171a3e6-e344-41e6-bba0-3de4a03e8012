package com.sansec.ai.portal.listener;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sansec.ai.common.base.entity.BaseEntity;
import com.sansec.ai.extra.dify.service.DifyDeployService;
import com.sansec.ai.portal.constant.PortalStatusEnum;
import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.ai.portal.service.PortalInfoService;
import com.sansec.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 启动门户事件
 *
 * <AUTHOR>
 * @since 2025/5/10 10:16
 */
@Slf4j
@Component
public class PortalStopEventListener implements ApplicationListener<PortalStopEvent> {


	@Autowired
	private PortalInfoService portalInfoService;
	@Autowired
	private DifyDeployService difyDeployService;

	@Async
	@Override
	public void onApplicationEvent(PortalStopEvent event) {
		log.info("异步停止Dify实例-开始[eventId={}]", event.getSource());
		try {
			difyDeployService.stopInstance(event.portalInfo.getPortalCode());
			portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
					.eq(PortalInfo::getId, event.portalInfo.getId())
					.set(PortalInfo::getStatus, PortalStatusEnum.STOPPED.code)
					.set(PortalInfo::getStatusDesc, "已停止")
					.set(BaseEntity::getUpdateTime, new Date()));
		} catch (Exception e) {
			log.error("停止Dify实例-报错", e);
			updatePortalOfError(event, e instanceof BusinessException ? e.getLocalizedMessage() : "停止过程出现异常，已强制停止");
		}
		log.info(String.format("异步停止Dify实例-结束[eventId=%s]", event.getSource()));
	}

	/**
	 * 初始化失败，更新门户状态
	 *
	 * @param event
	 * @param message
	 */
	private void updatePortalOfError(PortalStopEvent event, String message) {
		portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
				.eq(PortalInfo::getId, event.portalInfo.getId())
				.set(PortalInfo::getStatus, PortalStatusEnum.STOPPED.code)
				.set(PortalInfo::getStatusDesc, message)
				.set(BaseEntity::getUpdateTime, new Date()));
	}

}
