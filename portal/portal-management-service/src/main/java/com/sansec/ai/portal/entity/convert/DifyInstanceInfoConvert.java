package com.sansec.ai.portal.entity.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ai.portal.entity.po.DifyInstanceInfo;
import com.sansec.ai.portal.entity.vo.DifyInstanceInfoVo;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * Dify实例信息;
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
@Mapper(componentModel = "spring")
public interface DifyInstanceInfoConvert {

	@Mappings({
			@Mapping(source = "size", target = "pageSize"),
			@Mapping(source = "current", target = "pageNum"),
			@Mapping(source = "records", target = "list")
	})
	SecPageVO<DifyInstanceInfoVo> pagePOToSecPageVOPage(IPage<DifyInstanceInfo> iPage);

	@InheritConfiguration(name = "convertVo")
	List<DifyInstanceInfoVo> convert(List<DifyInstanceInfo> list);

	@Mappings({})
	DifyInstanceInfoVo convertVo(DifyInstanceInfo request);
}