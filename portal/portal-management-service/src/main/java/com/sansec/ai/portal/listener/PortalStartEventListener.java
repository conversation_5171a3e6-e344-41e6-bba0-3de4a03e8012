package com.sansec.ai.portal.listener;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sansec.ai.common.base.entity.BaseEntity;
import com.sansec.ai.extra.dify.result.DifyInstanceStatusVo;
import com.sansec.ai.extra.dify.service.DifyDeployService;
import com.sansec.ai.portal.constant.PortalStatusEnum;
import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.ai.portal.service.PortalInfoService;
import com.sansec.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 启动门户事件
 *
 * <AUTHOR>
 * @since 2025/5/10 10:16
 */
@Slf4j
@Component
public class PortalStartEventListener implements ApplicationListener<PortalStartEvent> {


	@Autowired
	private PortalInfoService portalInfoService;
	@Autowired
	private DifyDeployService difyDeployService;

	@Async
	@Override
	public void onApplicationEvent(PortalStartEvent event) {
		log.info("异步启动Dify实例-开始[eventId={}]", event.getSource());

		try {
			difyDeployService.startInstance(event.portalInfo.getPortalCode());
		} catch (Exception e) {
			log.error(String.format("异步启动Dify实例-报错[eventId=%s]", event.getSource()), e);
			updatePortalOfError(event, e instanceof BusinessException ? e.getLocalizedMessage() : "实例启动失败，状态异常");
			return;
		}

		//更新门户状态
		checkPortalOfSuccess(event.portalInfo);
		log.info(String.format("异步启动Dify实例-结束[eventId=%s]", event.getSource()));

	}

	/**
	 * 初始化失败，更新门户状态
	 *
	 * @param event
	 * @param message
	 */
	private void updatePortalOfError(PortalStartEvent event, String message) {
		portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
				.eq(PortalInfo::getId, event.portalInfo.getId())
				.set(PortalInfo::getStatus, PortalStatusEnum.ABNORMAL.code)
				.set(PortalInfo::getStatusDesc, message)
				.set(BaseEntity::getUpdateTime, new Date()));
	}

	/**
	 * 初始化成功，检查门户状态
	 *
	 * @param portalInfo
	 */
	private void checkPortalOfSuccess(PortalInfo portalInfo) {
		boolean isHealthy = false;
		for (int i = 0; i < 3; i++) {
			try {
				Thread.sleep(20000L);
			} catch (Exception e) {
				log.error("检查门户状态[portalCode={}]", portalInfo.getPortalCode(), e);
			}
			DifyInstanceStatusVo statusVo = difyDeployService.checkInstanceStatus(portalInfo.getPortalCode());
			if (!statusVo.getHealth()) {
				log.error("当前门户状态检查异常，等待重试[portalCode={}]", portalInfo.getPortalCode());
				continue;
			}
			isHealthy = true;
			break;
		}
		if (isHealthy) {
			portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
					.eq(PortalInfo::getId, portalInfo.getId())
					.set(PortalInfo::getStatus, PortalStatusEnum.RUNNING.code)
					.set(PortalInfo::getStatusDesc, " ")
					.set(BaseEntity::getUpdateTime, new Date()));
		} else {
			portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
					.eq(PortalInfo::getId, portalInfo.getId())
					.set(PortalInfo::getStatus, PortalStatusEnum.ABNORMAL.code)
					.set(PortalInfo::getStatusDesc, "当前门户状态检查异常")
					.set(BaseEntity::getUpdateTime, new Date()));
		}
	}
}
