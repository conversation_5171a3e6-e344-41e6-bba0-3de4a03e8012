package com.sansec.ai.portal.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sansec.ai.common.base.entity.BaseEntity;
import com.sansec.ai.common.base.enums.EnumDelFlag;
import com.sansec.ai.extra.dify.result.DifyInstanceStatusVo;
import com.sansec.ai.extra.dify.service.DifyDeployService;
import com.sansec.ai.portal.constant.PortalStatusEnum;
import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.ai.portal.service.PortalInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 门户报错补偿机制
 *
 * <AUTHOR>
 * @since 2025/5/29 19:24
 */
@Slf4j
@Component
public class PortalErrorCheckTask {

	/**
	 * 初始化中状态超时时长
	 */
	private static final int FLAG_INITIALIZING_TIMEOUT_MINUTE = 10;
	/**
	 * 删除中状态超时时长
	 */
	private static final int FLAG_DELETING_TIMEOUT_MINUTE = 5;
	/**
	 * 停止中状态超时时长
	 */
	private static final int FLAG_STOPPING_TIMEOUT_MINUTE = 5;
	/**
	 * 启动中状态超时时长
	 */
	private static final int FLAG_STARTING_TIMEOUT_MINUTE = 3;

	@Autowired
	private PortalInfoService portalInfoService;

	/**
	 * 定时检查错误的门户状态，每1min执行一次
	 * 检查所有中间状态，防止因网络异常、系统断电引起的门户状态流转逻辑无法闭环
	 */
	@Scheduled(initialDelay = 20 * 1000L, fixedDelay = 20 * 1000L)
	public void run() {
		log.info("门户报错补偿机制-开始");
		long currentTime = System.currentTimeMillis();
		//NOTE 1-门户在“初始化中”状态停留时间过长
		Calendar maxTime = Calendar.getInstance();
		maxTime.add(Calendar.MINUTE, -FLAG_INITIALIZING_TIMEOUT_MINUTE);
		List<PortalInfo> portalList = portalInfoService.list(new LambdaQueryWrapper<PortalInfo>()
				.eq(BaseEntity::getFlagDel, EnumDelFlag.NORMAL.getKey())
				.in(PortalInfo::getStatus, PortalStatusEnum.INITIALIZING.code)
				.lt(BaseEntity::getCreateTime, maxTime.getTime()));

		if (!portalList.isEmpty()) {
			for (PortalInfo portalInfo : portalList) {
				log.error("门户[portalCode={}]在“初始化中”状态停留时间过长", portalInfo.getPortalCode());
				portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
						.eq(PortalInfo::getId, portalInfo.getId())
						.set(PortalInfo::getStatus, PortalStatusEnum.INITIAL_ERROR.code)
						.set(PortalInfo::getStatusDesc, "门户初始化超时")
						.set(BaseEntity::getUpdateTime, new Date()));
			}
		}

		//NOTE 2-门户在“删除中”状态停留时间过长
		maxTime = Calendar.getInstance();
		maxTime.add(Calendar.MINUTE, -FLAG_DELETING_TIMEOUT_MINUTE);
		portalList = portalInfoService.list(new LambdaQueryWrapper<PortalInfo>()
				.eq(BaseEntity::getFlagDel, EnumDelFlag.NORMAL.getKey())
				.in(PortalInfo::getStatus, PortalStatusEnum.DELETING.code)
				.lt(BaseEntity::getUpdateTime, maxTime.getTime()));

		if (!portalList.isEmpty()) {
			for (PortalInfo portalInfo : portalList) {
				log.error("门户[portalCode={}]在“删除中”状态停留时间过长", portalInfo.getPortalCode());
				portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
						.eq(PortalInfo::getId, portalInfo.getId())
						.set(PortalInfo::getStatus, PortalStatusEnum.DELETE_ERROR.code)
						.set(PortalInfo::getStatusDesc, "删除门户超时")
						.set(BaseEntity::getUpdateTime, new Date()));
			}
		}

		//NOTE 3-门户在“停止中”状态停留时间过长
		maxTime = Calendar.getInstance();
		maxTime.add(Calendar.MINUTE, -FLAG_STOPPING_TIMEOUT_MINUTE);
		portalList = portalInfoService.list(new LambdaQueryWrapper<PortalInfo>()
				.eq(BaseEntity::getFlagDel, EnumDelFlag.NORMAL.getKey())
				.in(PortalInfo::getStatus, PortalStatusEnum.STOPPING.code)
				.lt(BaseEntity::getUpdateTime, maxTime.getTime()));

		if (!portalList.isEmpty()) {
			for (PortalInfo portalInfo : portalList) {
				log.error("门户[portalCode={}]在“停止中”状态停留时间过长", portalInfo.getPortalCode());
				portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
						.eq(PortalInfo::getId, portalInfo.getId())
						.set(PortalInfo::getStatus, PortalStatusEnum.STOPPED.code)
						.set(PortalInfo::getStatusDesc, "停止门户超时，已强制停止")
						.set(BaseEntity::getUpdateTime, new Date()));
			}
		}

		//NOTE 4-门户在“启动中”状态停留时间过长
		maxTime = Calendar.getInstance();
		maxTime.add(Calendar.MINUTE, -FLAG_STARTING_TIMEOUT_MINUTE);
		portalList = portalInfoService.list(new LambdaQueryWrapper<PortalInfo>()
				.eq(BaseEntity::getFlagDel, EnumDelFlag.NORMAL.getKey())
				.in(PortalInfo::getStatus, PortalStatusEnum.STARTING.code)
				.lt(BaseEntity::getUpdateTime, maxTime.getTime()));

		if (!portalList.isEmpty()) {
			for (PortalInfo portalInfo : portalList) {
				log.error("门户[portalCode={}]在“启动中”状态停留时间过长", portalInfo.getPortalCode());
				portalInfoService.update(new LambdaUpdateWrapper<PortalInfo>()
						.eq(PortalInfo::getId, portalInfo.getId())
						.set(PortalInfo::getStatus, PortalStatusEnum.ABNORMAL.code)
						.set(PortalInfo::getStatusDesc, "实例启动超时，状态异常")
						.set(BaseEntity::getUpdateTime, new Date()));
			}
		}

		log.info("门户报错补偿机制-结束[cost={}ms]", System.currentTimeMillis() - currentTime);
	}


}
