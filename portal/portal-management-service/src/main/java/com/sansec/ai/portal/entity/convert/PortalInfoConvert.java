package com.sansec.ai.portal.entity.convert;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sansec.ai.portal.entity.po.PortalInfo;
import com.sansec.ai.portal.entity.vo.PortalDetailVo;
import com.sansec.ai.portal.entity.vo.PortalListVo;
import com.sansec.ai.portal.entity.vo.PortalSiteInfoVo;
import com.sansec.common.param.response.SecPageVO;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * 门户信息;
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
@Mapper(componentModel = "spring")
public interface PortalInfoConvert {

	@Mappings({
			@Mapping(source = "size", target = "pageSize"),
			@Mapping(source = "current", target = "pageNum"),
			@Mapping(source = "records", target = "list")
	})
	SecPageVO<PortalListVo> toPagedPortalList(IPage<PortalListVo> page);

	@InheritConfiguration(name = "convertVo")
	List<PortalListVo> convert(List<PortalInfo> list);

	@Mappings({})
	PortalListVo convertVo(PortalInfo request);

	@Mappings({})
	PortalSiteInfoVo toSiteInfo(PortalInfo request);

	@Mappings({})
	PortalDetailVo toPortalDetail(PortalInfo request);
}