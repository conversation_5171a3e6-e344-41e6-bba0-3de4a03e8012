package com.sansec.ai.portal.entity.vo;

import lombok.Data;

import java.util.Date;

/**
 * 门户列表
 *
 * <AUTHOR>
 * @since 2025/5/7 16:36
 */
@Data
public class PortalListVo {

	/**
	 * ID
	 */
	private Long id;
	/**
	 * 门户名称
	 */
	private String portalName;
	/**
	 * 门户编码/URL前缀/实例前缀
	 */
	private String portalCode;
	/**
	 * 门户标题
	 */
	private String portalTitle;
	/**
	 * 门户logo，base64
	 */
	private String portalLogo;
	/**
	 * 实例ID
	 */
	private Long instanceId;
	/**
	 * 默认应用ID
	 */
	private String defaultAppId;
	/**
	 * 默认应用名称
	 */
	private String defaultAppName;
	/**
	 * 门户状态{@link com.sansec.ai.portal.constant.PortalStatusEnum}
	 */
	private Integer status;
	/**
	 * 状态描述
	 */
	private String statusDesc;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 门户管理员ID
	 */
	private Long userId;
	/**
	 * 门户管理员名称
	 */
	private String userName;

}
