package com.sansec.ai.portal.entity.request;

import com.sansec.ai.common.base.constant.FieldRegex;
import com.sansec.ai.common.base.paramverify.Verify;
import lombok.Data;

/**
 * 创建门户请求
 *
 * <AUTHOR>
 * @since 2025/5/7 16:36
 */
@Data
public class PortalCreateRequest {

	/**
	 * 门户名称
	 */
	@Verify(notBlank = true, regular = FieldRegex.LETTER_CHINESE_NUM_2LINE_32)
	private String portalName;
	/**
	 * 门户编码/URL前缀/实例前缀
	 */
	@Verify(notBlank = true, regular = FieldRegex.LETTER_CHINESE_NUM)
	private String portalCode;
	/**
	 * 门户标题
	 */
	@Verify(notBlank = true, regular = FieldRegex.LETTER_CHINESE_NUM_2LINE_32)
	private String portalTitle;
	/**
	 * 门户logo，base64
	 */
	@Verify(notNull = true)
	private String portalLogo;
	/**
	 * 实例管理员ID**新建管理员时为空**
	 */
	private Long userId;
	/**
	 * 实例管理员账号
	 */
	@Verify(notBlank = true)
	private String userAccount;
	/**
	 * 实例管理员密码
	 */
	@Verify(notBlank = true)
	private String userPassword;

}
