package com.sansec.ai.portal.service;

import com.sansec.ai.portal.entity.vo.DifyAccountInfo;
import com.sansec.ai.portal.entity.vo.DifyInstanceInfoVo;
import com.sansec.ai.portal.entity.vo.PortalSiteInfoVo;

/**
 * 向内部其它模块提供的门户接口
 *
 * <AUTHOR>
 * @since 2025/5/9 16:48
 */
public interface IPortalService {

	/**
	 * 获取门户网站配置
	 *
	 * @param portalId
	 * @return
	 */
	PortalSiteInfoVo getPortalSiteInfo(Long portalId);

	/**
	 * 获取门户网站配置
	 *
	 * @param portalCode
	 * @return
	 */
	PortalSiteInfoVo getPortalSiteInfo(String portalCode);

	/**
	 * 根据门户ID，获取Dify实例信息
	 *
	 * @param portalId
	 * @return
	 */
	DifyInstanceInfoVo getDifyInstanceInfoByPortalId(Long portalId);

	/**
	 * 根据门户前缀，获取Dify实例信息
	 *
	 * @param portalCode
	 * @return
	 */
	DifyInstanceInfoVo getDifyInstanceInfoByPortalCode(String portalCode);

	/**
	 * 根据IP和端口，获取Dify实例的管理员账号信息
	 *
	 * @param ip   IP地址
	 * @param port 端口号
	 * @return Dify实例信息
	 */
	DifyAccountInfo getDifyInstanceInfoByIpPort(String ip, int port);
}
