package com.sansec.ai.portal.constant;

/**
 * 门户状态
 *
 * <AUTHOR>
 * @since 2025/5/8 10:55
 */
public enum PortalStatusEnum {

	/**
	 * 初始化中
	 */
	INITIALIZING(1, "初始化中"),
	/**
	 * 初始化失败
	 */
	INITIAL_ERROR(2, "初始化失败"),
	/**
	 * 运行中
	 */
	RUNNING(3, "运行中"),
	/**
	 * 已停止
	 */
	STOPPED(4, "已停止"),
	/**
	 * 启动中
	 */
	STARTING(5, "启动中"),
	/**
	 * 异常
	 */
	ABNORMAL(6, "异常"),
	/**
	 * 删除中
	 */
	DELETING(7, "删除中"),
	/**
	 * 删除失败
	 */
	DELETE_ERROR(8, "删除失败"),
	/**
	 * 停止中
	 */
	STOPPING(9, "停止中"),

	//
	;

	public final int code;
	public final String description;

	PortalStatusEnum(int code, String description) {
		this.code = code;
		this.description = description;
	}


	public static PortalStatusEnum getByCode(int code) {
		for (PortalStatusEnum status : PortalStatusEnum.values()) {
			if (status.code == code) {
				return status;
			}
		}
		return DELETE_ERROR;
	}
}
