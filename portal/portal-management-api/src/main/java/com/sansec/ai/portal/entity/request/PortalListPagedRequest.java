package com.sansec.ai.portal.entity.request;

import com.sansec.common.param.request.SecPageDTO;
import lombok.Data;

/**
 * 分页获取门户列表请求
 *
 * <AUTHOR>
 * @since 2025/5/7 16:36
 */
@Data
public class PortalListPagedRequest extends SecPageDTO {

	/**
	 * 门户ID
	 */
	private Long portalId;
	/**
	 * 门户名称
	 */
	private String portalName;
	/**
	 * 门户编码/URL前缀/实例前缀
	 */
	private String portalCode;
	/**
	 * 门户标题
	 */
	private String portalTitle;
	/**
	 * 门户状态{@link com.sansec.ai.portal.constant.PortalStatusEnum}
	 */
	private Integer status;


}
