package com.sansec.ai.portal.constant;

/**
 * 门户相关错误码
 *
 * <AUTHOR>
 * @since 2025/5/15
 */
public interface PortalErrorCodeConst {

    /**
     * 门户不存在
     */
    String PORTAL_NOT_EXIST = "00060900";
    /**
     * 门户状态不支持当前操作
     */
    String PORTAL_STATUS_NOT_SUPPORT = "00060901";
    /**
     * 门户创建失败[%s]
     */
    String PORTAL_CREATE_FAILED_S = "00060902";
    /**
     * 门户初始化失败[%s]
     */
    String PORTAL_INIT_FAILED_S = "00060903";
    /**
     * 门户启动失败[%s]
     */
    String PORTAL_START_FAILED_S = "00060904";
    /**
     * 门户停止失败[%s]
     */
    String PORTAL_STOP_FAILED_S = "00060905";
    /**
     * 门户删除失败[%s]
     */
    String PORTAL_DELETE_FAILED_S = "00060906";
    /**
     * Dify实例不存在
     */
    String DIFY_INSTANCE_NOT_EXIST = "00060907";
    /**
     * 已创建门户数量超过最大限制
     */
    String PORTAL_MAX_COUNT_LIMITED = "00060908";
}
