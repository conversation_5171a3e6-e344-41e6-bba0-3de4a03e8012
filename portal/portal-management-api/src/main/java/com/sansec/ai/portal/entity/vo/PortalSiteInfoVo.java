package com.sansec.ai.portal.entity.vo;

import lombok.Data;

/**
 * 门户网站信息
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
@Data
public class PortalSiteInfoVo {
	/**
	 * 门户ID
	 */
	private Long id;
	/**
	 * 门户名称
	 */
	private String portalName;
	/**
	 * 门户编码/URL前缀/实例前缀
	 */
	private String portalCode;
	/**
	 * 门户标题
	 */
	private String portalTitle;
	/**
	 * 门户logo，base64
	 */
	private String portalLogo;
	/**
	 * 默认应用ID
	 */
	private String defaultAppId;
	/**
	 * 默认应用名称
	 */
	private String defaultAppName;
	/**
	 * 门户状态{@link com.sansec.ai.portal.constant.PortalStatusEnum}
	 */
	private Integer status;


}