#!/usr/bin/env bash
# The USR License (USR)
#
# Copyright (c) 2020 有人科技软件运维部
#
# Version:2.0
#
# JAVA程序运维脚本
#脚本使用帮助
DOC="\
Usage: dog-restart.sh [OPTIONS] \n\
  -h, --help            Show help and exit. \n\
  -p, --path            Set dog working directory.  \n\
                        Default value: the path of dog  \n\
  -s, --seconds         How many seconds to checking process start success \n\
                        Default value: 12
  -j, --jvm             Set the start param of JVM
                        Default value: -Xmx1G
  -c, --command         Set the start param of command line
"
JAVA_HOME="/opt/sansec/jdk/bin/"
# jar包名称
JAR_NAME=""
# 应用名称，同一个模块，两次的jar包名称可能不一样，但应用名称一定一样
APP_NAME=""
# 工作目录，默认为脚本所在目录
BIN_PATH=$(cd `dirname $0`; pwd)
# 预计启动完成需要消耗多少秒
START_EXPECT_SECONDS=20
# 预计停止程序需要消耗多少秒
STOP_EXPECT_SECONDS=6
# 初始化JVM参数设置
#START_JVM_PARAMS="-Xmx4G --add-opens java.base/java.lang=ALL-UNNAMED -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5006"
START_JVM_PARAMS="-Xmx1G --add-opens java.base/java.lang=ALL-UNNAMED -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./data/"
# 命令行参数，优先级：配置文件变量 < JVM系统变量 < 命令行参数
START_COMMAND_PARAMS="--spring.profiles.active=dev"

#脚本传参
GET_OPT_ARGS=`getopt -o p:s:j:c:h -al path:,seconds:,jvm:,command:,help -- "$@"`
eval set -- "${GET_OPT_ARGS}"
#获取参数
while [[ -n "$1" ]]
do
        case "$1" in
                -p|--path)      BIN_PATH=$2; shift 2;;
                -s|--seconds)   START_EXPECT_SECONDS=$2; shift 2;;
                -j|--jvm)       START_JVM_PARAMS=$2; shift 2;;
                -c|--command)   START_COMMAND_PARAMS=$2; shift 2;;
                -h|--help)      echo -e "${DOC}"; exit ;;
                *) break ;;
        esac
done

# 脚本初始化
function init(){
    if [[ ! -d ${BIN_PATH} ]]; then
        echo "\033[31m ${BIN_PATH}不存在，或不是有效的目录 \033[0m"
        exit 0
    fi
    if [[ ! ${JAR_NAME} ]]; then
        if [[ $(ls ${BIN_PATH}|grep .*.jar|wc -l) -gt 1 ]] ;then
            echo -e  "\033[31m ${BIN_PATH}目录中含有多个jar文件 \033[0m"
            exit 0
        fi
        # jar包名称
        JAR_NAME=$(ls ${BIN_PATH}|grep .*.jar)
    fi
    if [[ ! ${JAR_NAME} ]]; then
        echo -e  "\033[31m ${BIN_PATH}下未找到jar文件 \033[0m"
        exit 0
    fi
    #去掉prod前缀，兼容老的命名规范
    APP_NAME=${APP_NAME#*prod-}
    #去掉扩展名
    APP_NAME=${JAR_NAME%.jar*}
    #去掉版本号
    #APP_NAME=`echo ${APP_NAME}|tr -d [0123456789.]`
    #判断版本号：既包含"数字"又包含"."
#    if [[ ${APP_NAME} = `echo "$APP_NAME"|grep "[0-9]"|grep "[.]"` ]]; then
#        echo -e  "\033[31m jar包中包含版本号：$APP_NAME\033[0m"
#        exit 1
#    fi
}

# 检查进程是否在运行状态
function check_if_process_is_running {
    PID=$(ps aux | grep ${APP_NAME} | grep -v 'grep\|\.sh' | awk '{print $2}' )
    if [[ "$PID" = "" ]]; then
        return 1
    fi
        return $?
}

# 停止程序
function stop() {
    if ! check_if_process_is_running ;then
        echo -e  "\033[32m $APP_NAME already stopped \033[0m"
        return 0
    else
        kill ${PID}
        echo -e  "\033[32m waiting for process to safely stop \033[0m"
        NOT_KILLED=1
        # 循环检查程序是否已经停止
        for i in $(seq 1 ${STOP_EXPECT_SECONDS}); do
            if check_if_process_is_running ;then
                echo -ne "\033[32m.\033[0m"
                sleep 1
            else
                NOT_KILLED=0
                echo -e  "\033[32m $APP_NAME has stopped \033[0m"
                return 0
            fi
        done
        # 如果安全停止进程失败，则强制停止
        if [[ ${NOT_KILLED} = 1 ]] ;then
            echo -e  "\033[31m $APP_NAME stop failed, try to stop forcing \033[0m"
            kill -9 ${PID}
            # 循环检查程序是否已经停止
            for i in {1..3}; do
                if check_if_process_is_running ;then
                    sleep 1
                else
                    NOT_KILLED=0
                    echo -e  "\033[32m $APP_NAME has stopped \033[0m"
                    return 0
                fi
            done
        else
            echo -e  "\033[32m $APP_NAME has stopped \033[0m"
            return 0
        fi
        # 强制停止失败
        if [[ ${NOT_KILLED} = 1 ]]
        then
            echo -e  "\033[31m $APP_NAME stop failed, you cannot kill process \033[0m"
            return 2
        fi
    fi
}

# 启动程序
function start() {
    if check_if_process_is_running && [[ "$PID" != "" ]] ;then
        echo -e  "\033[32m $APP_NAME already running \033[0m"
        exit 1
    fi
    ## 加载环境变量，避免程序启动异常
    source /etc/profile
    ## 进入程序所在目录，启动程序
    cd ${BIN_PATH}
    echo "nohup ${JAVA_HOME}/java ${START_JVM_PARAMS} -jar ${JAR_NAME} ${START_COMMAND_PARAMS} --path=${BIN_PATH} >/dev/null 2>&1 &"
    nohup ${JAVA_HOME}/java ${START_JVM_PARAMS} -jar ${JAR_NAME} ${START_COMMAND_PARAMS} --path=${BIN_PATH} >/dev/null 2>&1 &
    #${JAVA_HOME}/java ${START_JVM_PARAMS} -jar ${JAR_NAME} ${START_COMMAND_PARAMS} --path=${BIN_PATH}
    echo -e   "\033[32m waiting for process to starting \033[0m"
    ## 循环等待
    for i in $(seq 1 ${START_EXPECT_SECONDS}); do
        sleep 1
    done

    if ! check_if_process_is_running; then
        echo -e  "\033[31m $APP_NAME failed to start \033[0m"
    else
        echo -e  "\033[32m $APP_NAME has started \033[0m"
    fi
}

init
stop
start