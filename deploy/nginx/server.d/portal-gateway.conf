#访问统一门户&应用网关
server {
        listen       9443 ssl;
        listen           [::]:9443 ssl;
        server_name  localhost;

        #charset koi8-r;
        #server_name_in_redirect off;
        #port_in_redirect off;
        absolute_redirect off;
                        access_log on;
        access_log  /opt/sansec/plat/nginx-gm/logs/gateway.access.log  log_json;
        #access_log  "pipe:rollback logs/host.access_log interval=1d baknum=7 maxsize=2G"  main;

        ssl_certificate certs/rsa_3072/rsa_server_3072.pem; # rsa 服务证书
        ssl_certificate_key lmk:certs/rsa_3072/rsa_server_3072.key; # rsa 服务密钥
        ssl_session_timeout 5m; # ssl session 超时 5 分钟
        ssl_protocols TLSv1.2 TLSv1.3; # ssl 协议版本
        ssl_ciphers TLS_AES_256_GCM_SHA384:TLS_AES_128_GCM_SHA256:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES128-GCM-SHA256;

        #add_header Access-Control-Allow-Origin *;
        #add_header Access-Control-Allow-Headers *;
        #add_header Access-Control-Allow-Methods *;
        # Add Strict-Transport-Security header
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Content-Security-Policy "upgrade-insecure-requests;connect-src *" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header X-Permitted-Cross-Domain-Policies "master-only" always;
        add_header X-Download-Options "nosniff" always;
        add_header X-Frame-Options "SAMEORIGIN" always;


        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
                root   html;
        }

        #应用网关
        location / {
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Original-URL $scheme://$host:$server_port$request_uri;
                proxy_pass http://127.0.0.1:18083$request_uri;
        }


        #统一门户前端
        location /SecPortal {
                        alias /opt/sansec/web/SecPortal;
                        index index.html index.htm;
                        try_files $uri $uri/ /SecPortal/index.html;
        }

        #统一门户后端
#       location ~* ^/(ai/portal/|portal/|instance/) {
#               proxy_pass http://127.0.0.1:18082/;
#       }

        #统一门户后端
        location /ai/portal/ {
                        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_set_header Host $host;
                        proxy_set_header X-Real-IP $remote_addr;
                        proxy_set_header X-Original-URL $scheme://$host:$server_port$request_uri;
                        proxy_pass http://127.0.0.1:18082/;
        }

        #统一门户后端
        location /portal/ {
                        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_set_header Host $host;
                        proxy_set_header X-Real-IP $remote_addr;
                        proxy_set_header X-Original-URL $scheme://$host:$server_port$request_uri;
                        proxy_pass http://127.0.0.1:18082;
        }

        #统一门户后端
        location /instance/ {
                        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                        proxy_set_header Host $host;
                        proxy_set_header X-Real-IP $remote_addr;
                        proxy_set_header X-Original-URL $scheme://$host:$server_port$request_uri;
                        proxy_pass http://127.0.0.1:18082;
        }

}