user  root;
worker_processes  2;

#daemon  off;
#master_process  off;
#worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
error_log /dev/null;

#pid        logs/nginx.pid;

#ssl_engine sdf;

events {
    worker_connections  81920;
}

lmk_file /opt/sansec/plat/nginx-gm/conf/certs/lmk.bin;

http {
    include       mime.types;
    default_type  application/octet-stream;

    client_max_body_size  0;

    log_format  main '$remote_addr:$remote_port - $server_addr:$server_port [$time_local] "$request" '
                     '$status $body_bytes_sent "$http_referer" '
                     '"$http_user_agent" "$http_x_forwarded_for"';

    log_format log_json '{"remote_addr": "$remote_addr","uri": "$uri","args": "$args","request_method": "$request_method","request_time": $request_time,"upstream_response_time": $upstream_response_time,"upstream_connect_time": $upstream_connect_time,"upstream_header_time": $upstream_header_time,"response_body_size": $body_bytes_sent,"bytes_sent": $bytes_sent,"request_length": $request_length,"status": $status,"http_user_agent": "$http_user_agent","traceId": "$request_id"}';

        # 不缓存，支持流式输出
    proxy_cache off;  # 关闭缓存
    proxy_buffering off;  # 关闭代理缓冲
    chunked_transfer_encoding on;  # 开启分块传输编码
    tcp_nopush on;  # 开启TCP NOPUSH选项，禁止Nagle算法
    tcp_nodelay on;  # 开启TCP NODELAY选项，禁止延迟ACK算法
    keepalive_timeout 300;  # 设定keep-alive超时时间为65秒


    #access_log  logs/access.log  main;

    sendfile        on;
    #keepalive_timeout   10m;
    keepalive_requests  65535;

    send_timeout        10m;
    proxy_read_timeout  10m;
    proxy_send_timeout  10m;

    underscores_in_headers  on;

    ssl_session_timeout     8h;
    ssl_session_cache       shared:WEB:32m;
    ssl_session_tickets     on;

    # generate: openssl rand 80 > conf/ticket.key
    #ssl_session_ticket_key  conf/ticket.key;

    #gzip  on;

    #include conf.d/*.conf;
        server_tokens off;

        server {
                listen       19443 ssl;
                listen           [::]:19443 ssl;
                server_name  localhost;

                #charset koi8-r;
                #server_name_in_redirect off;
                #port_in_redirect off;
                absolute_redirect off;
                access_log off;
                #access_log  logs/host.access.log  main;
                #access_log  "pipe:rollback logs/host.access_log interval=1d baknum=7 maxsize=2G"  main;

                ssl_certificate certs/rsa_3072/rsa_server_3072.pem; # rsa 服务证书
                ssl_certificate_key lmk:certs/rsa_3072/rsa_server_3072.key; # rsa 服务密钥
                ssl_session_timeout 5m; # ssl session 超时 5 分钟
                ssl_protocols TLSv1.2 TLSv1.3; # ssl 协议版本
                ssl_ciphers TLS_AES_256_GCM_SHA384:TLS_AES_128_GCM_SHA256:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES128-GCM-SHA256;

                #add_header Access-Control-Allow-Origin *;
                #add_header Access-Control-Allow-Headers *;
                #add_header Access-Control-Allow-Methods *;
                # Add Strict-Transport-Security header
                add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
                add_header X-Content-Type-Options "nosniff" always;
                add_header X-XSS-Protection "1; mode=block" always;
                add_header Content-Security-Policy "upgrade-insecure-requests;connect-src *" always;
                add_header Referrer-Policy "no-referrer-when-downgrade" always;
                add_header X-Permitted-Cross-Domain-Policies "master-only" always;
                add_header X-Download-Options "nosniff" always;
                add_header X-Frame-Options "SAMEORIGIN" always;

                include conf.d/*.conf;

                error_page   500 502 503 504  /50x.html;
                location = /50x.html {
                        root   html;
                }
        }
        include server.d/*.conf;
}