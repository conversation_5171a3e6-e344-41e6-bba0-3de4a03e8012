location /SecAI {
    alias /opt/sansec/web/SecAI;
    index index.html index.htm;
    try_files $uri $uri/ /SecAI/index.html;
}

location /ai/ops/ {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Original-URL $scheme://$host:$server_port$request_uri;
        proxy_pass http://127.0.0.1:18080/ai/ops/;
}