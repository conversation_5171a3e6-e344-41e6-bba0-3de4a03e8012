#门户管理前端
location /sec-ai-web {
        alias /opt/sansec/web/sec-ai-web;
        index index.html index.htm;
        try_files $uri $uri/ /sec-ai-web/index.html;
}

#门户管理后端
location /ai/portal/ {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Original-URL $scheme://$host:$server_port$request_uri;
        proxy_pass http://127.0.0.1:18082/;
}