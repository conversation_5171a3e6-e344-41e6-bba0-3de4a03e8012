package com.sansec.ai.extra.dify.request;

import lombok.Data;

/**
 * Dify实例配置项
 *
 * <AUTHOR>
 * @since 2025/5/12 19:00
 */
@Data
public class DifyInstanceProperties {

	/**
	 * 实例编码，Dify实例唯一标识，对应门户编码
	 */
	private String instanceCode;

	/**
	 * 外部网关地址
	 */
	private String gatewayUrl;

	/**
	 * 用于安全签署会话cookie和加密数据库上敏感信息的密钥
	 * 示例: XD2GWKgcpUOOtpecz1mrXcy5NIZGMXJv/trTQ7fVG5Lbp2z9tcFZifkr
	 */
	private String secretKey;

	/**
	 * 服务对外暴露端口
	 */
	private Integer servicePort;

}
