package com.sansec.ai.extra.dify.entity;

import lombok.Data;

import java.util.List;

/**
 * 应用实体类，用于表示应用的相关信息
 */
@Data
public class AppEntity {
    /**
     * 应用的唯一标识符
     */
    private String id;

    /**
     * 应用的名称
     */
    private String name;

    /**
     * 应用的描述信息
     */
    private String description;

    /**
     * 应用的模式
     */
    private String mode;

    /**
     * 应用图标的类型
     */
    private String iconType;

    /**
     * 应用的图标
     */
    private String icon;

    /**
     * 应用图标背景的颜色
     */
    private String iconBackground;

    /**
     * 应用图标的URL
     */
    private String iconUrl;

    /**
     * 应用创建的时间戳
     */
    private Long createdAt;

    /**
     * 应用最后更新的时间戳
     */
    private Long updatedAt;

    // 标签列表
    private List<String> tags;
}
