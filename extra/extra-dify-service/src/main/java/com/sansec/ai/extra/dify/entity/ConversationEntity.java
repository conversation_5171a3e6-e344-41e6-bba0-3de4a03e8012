package com.sansec.ai.extra.dify.entity;

import lombok.Data;

import java.util.Date;

/**
 * 会话实体类，用于表示会话的相关信息
 */
@Data
public class ConversationEntity {
    /**
     * 会话的唯一标识符
     */
    private String conversationId;

    /**
     * 应用的唯一标识符
     */
    private String appId;

    /**
     * 会话名称
     */
    private String conversationName;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 应用的名称
     */
    private String appName;

    /**
     * 应用的图标
     */
    private String icon;

    /**
     * 图标的背景颜色
     */
    private String iconBackground;

    /**
     * 图标的类型
     */
    private String iconType;

    /**
     * 来源URL
     */
    private String sourceUrl;

    private String userId;

}
