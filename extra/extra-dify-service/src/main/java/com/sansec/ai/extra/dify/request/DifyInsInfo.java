package com.sansec.ai.extra.dify.request;

import lombok.Data;

@Data
public class DifyInsInfo {

    private String ip;

    private int port;

    public DifyInsInfo(int port) {
        this.ip = "host.docker.internal";
        this.port = port;
    }

    public DifyInsInfo(String ip, int port) {
        this.ip = ip;
        this.port = port;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        DifyInsInfo that = (DifyInsInfo) o;

        if (port != that.port) {
            return false;
        }
        return ip.equals(that.ip);
    }

    @Override
    public int hashCode() {
        int result = ip.hashCode();
        result = 31 * result + port;
        return result;
    }
}
