package com.sansec.ai.extra.dify.util;

import com.sansec.ai.extra.dify.entity.ModelConfigParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.Yaml;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模型配置解析器
 * <p>
 * 用于解析YAML格式的模型配置文件，加载模型参数和系统默认模型配置
 * </p>
 */
@Service
@Slf4j
public class ModelConfigParser {

    /**
     * 模型配置文件路径
     */
    public static final String MODEL_CONFIG_FILE = "./config/model_config.yaml";

    /**
     * 加载模型配置列表
     * <p>
     * 从配置文件中读取所有模型的配置参数，并转换为ModelConfigParam对象列表
     * </p>
     *
     * @return 模型配置参数列表
     */
    public List<ModelConfigParam> loadModelConfig() {
        log.info("开始加载模型配置文件: {}", MODEL_CONFIG_FILE);
        Yaml yaml = new Yaml();
        List<ModelConfigParam> modelConfigParamList = new ArrayList<>();
        try (InputStream in = new FileInputStream(MODEL_CONFIG_FILE)) {
            Map<String, Object> data = yaml.load(in);
            if (data == null || !data.containsKey("models")) {
                log.error("模型配置文件格式错误或不包含models节点");
                return modelConfigParamList;
            }

            List<Map<String, Object>> modelList = (List<Map<String, Object>>) data.get("models");
            log.info("找到{}个模型配置", modelList.size());

            for (Map<String, Object> modelMap : modelList) {
                try {
                    // 创建模型配置对象并设置属性
                    ModelConfigParam config = new ModelConfigParam();
                    config.setName(modelMap.get("name").toString());
                    config.setDisplayName(modelMap.get("displayName").toString());
                    config.setModelType(modelMap.get("modelType").toString());
                    config.setApiKey(modelMap.get("apiKey").toString());
                    config.setEndpointUrl(modelMap.get("endpointUrl").toString());
                    config.setCompletionMode(modelMap.get("completionMode").toString());
                    config.setContextSize(modelMap.get("contextSize").toString());
                    config.setMaxTokensToSample(modelMap.get("maxTokensToSample").toString());
                    config.setVisionSupport(modelMap.get("visionSupport").toString());

                    modelConfigParamList.add(config);
                    log.info("成功加载模型配置: {}", config.getName());
                } catch (Exception e) {
                    log.error("解析模型配置失败: {}", e.getMessage());
                }
            }

            log.info("模型配置加载完成，共加载{}个模型", modelConfigParamList.size());
            return modelConfigParamList;

        } catch (Exception e) {
            log.error("加载模型配置文件失败: {}", e.getMessage(), e);
        }
        return modelConfigParamList;
    }

    /**
     * 加载系统默认模型配置
     * <p>
     * 从配置文件中读取systemModels节点，获取各类型模型的默认配置
     * </p>
     *
     * @return 模型类型到模型名称的映射
     */
    public Map<String, String> loadSystemModelConfig() {
        log.info("开始加载系统默认模型配置");
        Yaml yaml = new Yaml();
        Map<String, String> systemModelMap = new HashMap<>();

        try (InputStream in = new FileInputStream(MODEL_CONFIG_FILE)) {
            Map<String, Object> data = yaml.load(in);
            if (data == null || !data.containsKey("systemModels")) {
                log.error("模型配置文件不包含systemModels节点");
                return systemModelMap;
            }

            // 将Object类型转换为Map<String, String>
            Map<String, Object> systemModels = (Map<String, Object>) data.get("systemModels");
            for (Map.Entry<String, Object> entry : systemModels.entrySet()) {
                if (entry.getValue() != null) {
                    systemModelMap.put(entry.getKey(), entry.getValue().toString());
                    log.info("加载系统默认模型配置: {} -> {}", entry.getKey(), entry.getValue());
                }
            }

            log.info("系统默认模型配置加载完成，共加载{}个配置", systemModelMap.size());
            return systemModelMap;
        } catch (Exception e) {
            log.error("加载系统默认模型配置失败: {}", e.getMessage(), e);
        }
        return systemModelMap;
    }

}
