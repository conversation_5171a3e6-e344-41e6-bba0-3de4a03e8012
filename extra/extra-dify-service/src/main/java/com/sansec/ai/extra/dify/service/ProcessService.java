package com.sansec.ai.extra.dify.service;

import com.sansec.ai.extra.dify.result.ProcessResultVo;

import java.io.IOException;

/**
 * 进程管理服务
 *
 * <AUTHOR>
 * @since 2025/5/20 10:51
 */
public interface ProcessService {


	/**
	 * 执行linux指令
	 *
	 * @param command
	 * @param scriptFile
	 * @param workingDirectory
	 * @param params
	 * @return
	 * @throws Exception
	 */
	String execCmdToString(String command, String scriptFile, String workingDirectory, String... params) throws Exception;

	/**
	 * 执行linux指令，并返回执行结果
	 *
	 * @param workDir       命令执行目录
	 * @param timeoutMillis 命令超时时间
	 * @param command
	 * @param params
	 * @return
	 */
	ProcessResultVo execCommandWithResult(String workDir, Long timeoutMillis, String command, String... params);

}
