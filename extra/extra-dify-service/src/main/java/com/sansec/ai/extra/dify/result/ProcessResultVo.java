package com.sansec.ai.extra.dify.result;

import lombok.Data;

/**
 * 执行命令响应结果
 *
 * <AUTHOR>
 */
@Data
public class ProcessResultVo {
	/**
	 * 是否成功
	 */
	private Boolean success;
	/**
	 * 命令执行结束状态码
	 */
	private Integer exitValue;
	/**
	 * 命令执行过程中的控制台输出内容
	 */
	private String console;

	public ProcessResultVo(boolean success) {
		this(success, null, null);
	}

	public ProcessResultVo(boolean success, Integer exitValue) {
		this(success, exitValue, null);
	}

	public ProcessResultVo(boolean success, Integer exitValue, String console) {
		this.success = success;
		this.exitValue = exitValue;
		this.console = console;
	}

}