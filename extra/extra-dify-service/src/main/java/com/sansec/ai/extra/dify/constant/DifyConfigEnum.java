package com.sansec.ai.extra.dify.constant;


import com.sansec.ai.common.constant.IConfigCode;

/**
 * Dify配置枚举类<br>
 * 系统配置表的统一约束，用于规范查询系统配置项的过程<br>
 * 各模块在从系统配置表查询系统配置项时，必须定义枚举，实现该接口
 * 缓存对象的类型对应的枚举，用于规范缓存中的数据<br>
 * 使用缓存时必须在此声明key，然后通过key获取value
 *
 * <AUTHOR>
 * @since 2025-5-7
 */
public enum DifyConfigEnum implements IConfigCode {
	/**
	 * Dify所有配置项
	 */
	ALL_CONFIG("dify_config"),
	/**
	 * dify默认应用ID
	 */
	DEFAULT_APP_ID("dify_config", "default_app_id"),
	/**
	 * dify默认应用名称
	 */
	DEFAULT_APP_NAME("dify_config", "default_app_id"),
	/**
	 * Dify实例默认端口
	 */
	DEFAULT_PORT("dify_config", "default_port"),
	/**
	 * 模板数据库名称——业务数据库
	 */
	TEMPLATE_DATABASE_NAME_DIFY("dify_config", "template_database_name_dify"),
	/**
	 * 模板数据库名称——插件数据库
	 */
	TEMPLATE_DATABASE_NAME_DIFY_PLUGIN("dify_config", "template_database_name_dify_plugin"),
	/**
	 * 统一数据库连接账号
	 */
	DATABASE_USERNAME("dify_config", "database_username"),
	/**
	 * 统一数据库连接密码
	 */
	DATABASE_PASSWORD("dify_config", "database_password"),
	/**
	 * dify的docker容器名称后缀
	 */
	DOCKER_CONTAINER_NAME_SUFFIX("docker_container_name_suffix_list"),
	/**
	 * env模板文件，配置项集合
	 * <pre>
	 *     code: env文件中的占位符key
	 *     value: 对应{@link com.sansec.ai.extra.dify.request.DifyInstanceProperties}中的field
	 *     例：
	 *          code=_CONSOLE_API_URL，value=gatewayUrl
	 *          代表把env文件中的${_CONSOLE_API_URL}替换为DifyInstanceProperties.getGatewayUrl()
	 * </pre>
	 */
	TEMPLATE_ENV_CONFIG_MAP("template_env_config_map"),

	//
	;

	/**
	 * key值前缀
	 */
	private final String typeCode;
	/**
	 * 超时时间
	 */
	private String code;


	DifyConfigEnum(String typeCode) {
		this.typeCode = typeCode;
	}

	DifyConfigEnum(String typeCode, String code) {
		this.typeCode = typeCode;
		this.code = code;
	}

	/**
	 * 获取配置项类型编码
	 *
	 * @return
	 */
	@Override
	public String getTypeCode() {
		return typeCode;
	}

	/**
	 * 获取配置项编码
	 *
	 * @return
	 */
	@Override
	public String getCode() {
		return code;
	}
}
