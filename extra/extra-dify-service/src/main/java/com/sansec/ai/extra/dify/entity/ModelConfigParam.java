package com.sansec.ai.extra.dify.entity;

import lombok.Data;

@Data
public class ModelConfigParam {

    // 模型名称
    private String name;

    // 模型显示名称
    private String displayName;

    // 模型类型   llm  rerank  text-embedding  speech2text  tts
    private String modelType;

    // API Key
    private String apiKey;

    // API endpoint URL
    private String endpointUrl;

    // Completion mode     chat  completion
    private String completionMode;

    // 模型上下文长度
    private String contextSize;

    // 最大 token 上限
    private String maxTokensToSample;

    // 是否支持视觉功能   no_support   support
    private String visionSupport;
}
