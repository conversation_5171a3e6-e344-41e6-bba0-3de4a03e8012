package com.sansec.ai.extra.dify.util;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.sansec.ai.extra.dify.constant.DifyCommonConstant;
import com.sansec.ai.extra.dify.constant.DifyErrorCode;
import com.sansec.ai.extra.dify.entity.LoginResponse;
import com.sansec.ai.extra.dify.request.DifyInsInfo;
import com.sansec.ai.portal.entity.vo.DifyAccountInfo;
import com.sansec.ai.portal.service.IPortalService;
import com.sansec.common.exception.BusinessException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class DifyTokenUtil {

	@Autowired
	private RestTemplate restTemplate;
	@Autowired
	private IPortalService portalService;

	//Key: dify实例的端口, Value: Token对象
	public static Map<DifyInsInfo, Token> tokens = new HashMap<>();

	@Data
	public static class Token {
		private String token;

		private Date genTime;

		public Token(String token, Date genTime) {
			this.token = token;
			this.genTime = genTime;
		}
	}

	/**
	 * 重新生成token
	 *
	 * @param difyIns
	 * @return
	 */
	public String getNewToken(DifyInsInfo difyIns) throws BusinessException {
		String newToken = generateNewToken(difyIns);
		tokens.put(difyIns, new Token(newToken, new Date()));
		return newToken;
	}

	public String getToken(DifyInsInfo difyIns) throws BusinessException {
		// 判断是否存在该dify实例的token，token有效期1h。如果不存在或即将过期（2分钟以内过期），则重新获取token。
		Token token = tokens.get(difyIns);
		if (token != null && token.getGenTime().after(new Date(System.currentTimeMillis() - 60 * 58 * 1000))) {
			return token.getToken();
		}
		String newToken = generateNewToken(difyIns);
		tokens.put(difyIns, new Token(newToken, new Date()));
		return newToken;
	}

	private String generateNewToken(DifyInsInfo difyIns) throws BusinessException {
		String url = "http://" + difyIns.getIp() + ":" + difyIns.getPort() + "/console/api/login";
		log.info("Constructed URL: {}", url);

		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);

		// 查库拿到账号密码
		DifyAccountInfo difyAccountInfo = portalService.getDifyInstanceInfoByIpPort(difyIns.getIp(), difyIns.getPort());
		if (difyAccountInfo == null || difyAccountInfo.getUsername() == null || difyAccountInfo.getDecPwd() == null) {
			log.error("Failed to retrieve Dify instance info for IP: {}, port: {}", difyIns.getIp(), difyIns.getPort());
			return null;
		}
		String requestBody = "{\"email\":\"" + difyAccountInfo.getUsername() + DifyCommonConstant.EMAIL_SUFFIX +
				"\",\"password\":\"" + difyAccountInfo.getDecPwd() + "\",\"language\":\"zh-Hans\",\"remember_me\":true}";
		log.debug("Request body: {}", requestBody);

		HttpEntity<String> request = new HttpEntity<>(requestBody, headers);
		log.info("Sending POST request to URL: {}", url);
		try {
			JSONObject responseJson = restTemplate.postForObject(url, request, JSONObject.class);
			if (responseJson == null || !responseJson.containsKey("result")) {
				throw new BusinessException(DifyErrorCode.TOKEN_GENERATE_ERROR);
			}
			if (!"success".equals(responseJson.getString("result"))) {
				throw new BusinessException(DifyErrorCode.TOKEN_GENERATE_ERROR);
			}
			LoginResponse response = restTemplate.postForObject(url, request, LoginResponse.class);
			log.info("Received valid response from dify");
			Map<String, String> data = response.getData();
			return data.get("access_token");
		} catch (RestClientException e) {
			log.error(String.format("从dify获取token抛错[dify=%s]", JSON.toJSONString(difyIns)), e);
			throw new BusinessException(DifyErrorCode.TOKEN_GENERATE_ERROR);
		}
	}
}
