package com.sansec.ai.extra.dify.service;

import com.alibaba.fastjson.JSONObject;
import com.sansec.ai.extra.dify.entity.AppEntity;
import com.sansec.ai.extra.dify.entity.ConversationEntity;
import com.sansec.ai.extra.dify.request.DifyInsInfo;
import com.sansec.common.param.response.SecPageVO;

import java.util.List;

/**
 * Dify业务相关接口
 */
public interface DifyApiService {

	/**
	 * 重置用户密码
	 *
	 * @param difyInsInfo Dify实例信息对象
	 * @param userAccount 用户账户
	 * @param oldPwd      旧密码
	 * @param newPwd      新密码
	 */
	void resetPassword(DifyInsInfo difyInsInfo, String userAccount, String oldPwd, String newPwd);

	/**
	 * 获取应用列表
	 *
	 * @param difyInsInfo Dify实例信息对象
	 */
	SecPageVO<AppEntity> getAppList(DifyInsInfo difyInsInfo, String mode, int pageNum, int pageSize);

	/**
	 * 获取应用详情
	 *
	 * @param difyInsInfo Dify实例信息对象
	 */
	JSONObject getAppDetail(DifyInsInfo difyInsInfo, String appId);

	/**
	 * 获取指定应用的iframe URL
	 *
	 * @param difyInsInfo Dify实例信息对象
	 * @param appId       应用ID
	 */
	String getIframeUrl(DifyInsInfo difyInsInfo, String appId);

	/**
	 * 获取会话列表
	 *
	 * @param portalCode  门户标识
	 * @param userId      用户ID
	 * @param limit       返回的最大对话数
	 * @return            会话列表
	 */
	List<ConversationEntity> getConvserationList(String portalCode, String userId, int limit);

	/**
	 * 删除会话
	 */
	void removeConversation(String portalCode, String userId, String conversationId);

	/**
	 * 会话重命名
	 */
	void renameConversation(String portalCode, String userId, String conversationId, String newName);
}
