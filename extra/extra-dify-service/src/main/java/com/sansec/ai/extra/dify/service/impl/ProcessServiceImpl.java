package com.sansec.ai.extra.dify.service.impl;

import com.sansec.ai.extra.dify.result.ProcessResultVo;
import com.sansec.ai.extra.dify.service.ProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.exec.*;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @since 2025/5/20 10:52
 */
@Slf4j
@Service
public class ProcessServiceImpl implements ProcessService {


	@Override
	public String execCmdToString(String command, String scriptFile, String workingDirectory, String... params) throws Exception {
		// 标准输出：print （null if watchdog timeout）
		// 错误输出：logging + 异常 （still exists if watchdog timeout）
		// 标准输入
		if (StringUtils.isAnyBlank(command, scriptFile, workingDirectory)) {
			return "参数不全";
		}
		log.info("指令为：{}，脚本为：{}，工作路径为：{}", command, scriptFile, workingDirectory);
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		try {
			PumpStreamHandler streamHandler = new PumpStreamHandler(outputStream, outputStream);
			execCmd(command, scriptFile, new File(workingDirectory), params, streamHandler);
		} catch (ExecuteException e) {
			log.error("执行脚本异常详情为:{}", e.getMessage());
			return "执行脚本异常返回码为:" + e.getExitValue();
		} catch (IOException e) {
			return "执行脚本失败";
		} finally {
			try {
				outputStream.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return outputStream.toString(StandardCharsets.UTF_8);
	}

	@Override
	public ProcessResultVo execCommandWithResult(String workDir, Long timeoutMillis, String command, String... params) {
		log.info("执行命令[workDir={}, timeoutMillis={}, command={}, params={}]", workDir, timeoutMillis, command, StringUtils.join(params, " "));
		if (!ArrayUtils.isEmpty(params)) {
			command += " " + StringUtils.join(params, " ");
			// 该方式有问题：当params数组中一个元素包含多个参数时，会被识别为一个参数
//            commandLine.addArguments(params, false);
		}
		CommandLine commandLine = CommandLine.parse(command);
		DefaultExecutor executor = new DefaultExecutor();
		//执行目录
		if (StringUtils.isNotBlank(workDir)) {
			File dir = new File(workDir);
			if (!dir.isDirectory()) {
				log.error("{}不是目录", workDir);
				return new ProcessResultVo(false, null, String.format("%s不是目录", workDir));
			} else if (!dir.exists()) {
				log.error("{}目录不存在", workDir);
				return new ProcessResultVo(false, null, String.format("%s目录不存在", workDir));
			} else {
				executor.setWorkingDirectory(dir);
			}
		}
		executor.setExitValues(null);
		//超时时间
		executor.setWatchdog(new ExecuteWatchdog(timeoutMillis));
		//接收控制台输出结果
		ByteArrayOutputStream out = new ByteArrayOutputStream();
		PumpStreamHandler streamHandler = new PumpStreamHandler(out, out);
		executor.setStreamHandler(streamHandler);
		try {
			int exitValue = executor.execute(commandLine);
			//根据系统编码格式，获取控制台输出
			String console = out.toString(Charset.defaultCharset().displayName());
			log.info("命令执行结果: {}", console);
			return new ProcessResultVo(true, exitValue, console);
		} catch (Exception e) {
			log.error(String.format("执行命令抛错[command=%s]", command), e);
			return new ProcessResultVo(false, null, null);
		} finally {
			try {
				out.close();
			} catch (Exception e) {

			}
		}
	}

	/**
	 * @param command
	 * @param scriptFile
	 * @param workingDirectory
	 * @param params
	 * @param streamHandler
	 * @return
	 * @throws IOException
	 */
	private int execCmd(String command, String scriptFile, File workingDirectory, String[] params, PumpStreamHandler streamHandler) throws IOException {
		CommandLine commandline = new CommandLine(command);
		if (!org.springframework.util.StringUtils.isEmpty(scriptFile)) {
			commandline.addArgument(scriptFile);
		}
		if (params != null && params.length > 0) {
			commandline.addArguments(params);
		}
		//设置超时时间：10秒
		ExecuteWatchdog watchdog = new ExecuteWatchdog(1000 * 10);
		// execCmd
		DefaultExecutor exec = new DefaultExecutor();
		int[] ints = {0, 1, 2, 127, 130};

		exec.setExitValues(ints);
		exec.setWatchdog(watchdog);
		exec.setStreamHandler(streamHandler);
		exec.setWorkingDirectory(workingDirectory);
		int exitValue = exec.execute(commandline);// exit code: 0=success, 1=error
		log.info("shell 脚本执行退出码为：{}", exitValue);
		System.out.println("shell 脚本执行退出码为：" + exitValue);
		return exitValue;
	}

}
