package com.sansec.ai.extra.dify.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.sansec.ai.extra.dify.constant.DifyConfigEnum;
import com.sansec.ai.extra.dify.constant.DifyErrorCode;
import com.sansec.ai.extra.dify.entity.AppEntity;
import com.sansec.ai.extra.dify.entity.ConversationEntity;
import com.sansec.ai.extra.dify.request.DifyInsInfo;
import com.sansec.ai.extra.dify.service.DifyApiService;
import com.sansec.ai.extra.dify.util.DifyTokenUtil;
import com.sansec.ai.portal.entity.vo.DifyInstanceInfoVo;
import com.sansec.ai.portal.service.IPortalService;
import com.sansec.common.exception.BusinessException;
import com.sansec.common.param.response.SecPageVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.sql.*;
import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Dify业务相关接口
 */
@Service
@Slf4j
public class DifyApiServiceImpl extends BaseDifyService implements DifyApiService {

	@Value("${ai.dify.instance_ip}")
	private String dbIP;
	@Value("${ai.dify.database_port}")
	private String dbPort;
	@Value("${spring.datasource.username}")
	private String dbUser;
	@Value("${spring.datasource.password}")
	private String dbPwd;
	@Autowired
	private DifyTokenUtil difyTokenUtil;
	@Autowired
	private IPortalService iPortalService;

	@Resource
	private Cache<String, String> appIconUrlCache;


	/**
	 * 重置用户密码
	 * 用于重置Dify实例中指定用户的密码
	 * 注：一期无需求，暂不实现
	 */
	@Override
	public void resetPassword(DifyInsInfo difyInsInfo, String userAccount, String oldPwd, String newPwd) {
		//一期无需求，暂不实现
	}

	/**
	 * 获取应用列表
	 * 从指定的Dify实例中获取应用列表，可根据模式进行过滤
	 * 返回的应用列表包含应用的基本信息，如ID、名称、描述等
	 */
	@Override
	public SecPageVO<AppEntity> getAppList(DifyInsInfo difyInsInfo, String mode, int pageNum, int pageSize) {
		String url = "http://" + difyInsInfo.getIp() + ":" + difyInsInfo.getPort() + "/console/api/apps?page=" + pageNum + "&limit=" + pageSize + "&name=";
		if (StringUtils.isNotBlank(mode)) {
			url += "&mode=" + mode;
		}
		log.info("Requesting app list from URL: {}", url);

		SecPageVO<AppEntity> result = new SecPageVO<>();
		result.setPageNum(pageNum);
		result.setPageSize(pageSize);
		JSONObject jsonObject = requestGet(difyInsInfo, url);
		List<AppEntity> resultList = new ArrayList<>();
		if (jsonObject == null || !jsonObject.containsKey("data") || jsonObject.getJSONArray("data") == null) {
			log.info("No data found or invalid JSON response.");
			result.setList(new ArrayList<>());
			result.setTotal(0);
			return result;
		}

		for (int i = 0; i < jsonObject.getJSONArray("data").size(); i++) {
			JSONObject app = jsonObject.getJSONArray("data").getJSONObject(i);
			AppEntity appEntity = new AppEntity();
			appEntity.setId(app.getString("id"));
			appEntity.setName(app.getString("name"));
			appEntity.setDescription(app.getString("description"));
			appEntity.setMode(app.getString("mode"));
			appEntity.setIconType(app.getString("icon_type"));
			appEntity.setIcon(app.getString("icon"));
			appEntity.setIconBackground(app.getString("icon_background"));
			appEntity.setIconUrl(app.getString("icon_url"));
			appEntity.setCreatedAt(app.getLong("created_at"));
			appEntity.setUpdatedAt(app.getLong("updated_at"));

			JSONArray tags = app.getJSONArray("tags");

			boolean notShowFlag = false;
			if (tags != null && tags.size() > 0) {
				List<String> tagList = new ArrayList<>();
				for (int j = 0; j < tags.size(); j++) {
					JSONObject tag = tags.getJSONObject(j);
					tagList.add(tag.getString("name"));
					if (tag.getString("name").equals("不展示")) {
						notShowFlag = true;
					}
				}
				appEntity.setTags(tagList);
			}
			if (notShowFlag) continue;
			resultList.add(appEntity);
		}

		log.info("Successfully retrieved {} apps.", resultList.size());
		result.setList(resultList);
		result.setTotal(jsonObject.getInteger("total"));
		return result;
	}

	@Override
	public JSONObject getAppDetail(DifyInsInfo difyInsInfo, String appId) {
		String url = "http://" + difyInsInfo.getIp() + ":" + difyInsInfo.getPort() + "/console/api/apps/" + appId;
		JSONObject jsonObject = requestGet(difyInsInfo, url);

		return jsonObject;
	}

	/**
	 * 获取应用的iframe URL
	 * 根据应用ID获取其对应的iframe嵌入URL
	 * 会根据应用的模式（workflow、chat、completion等）生成不同的URL路径
	 */
	@Override
	public String getIframeUrl(DifyInsInfo difyInsInfo, String appId) {
		if (StringUtils.isBlank(appId)) {
			appId = getConfigOrDefault(DifyConfigEnum.DEFAULT_APP_ID, "1");
		}
		String iframeUrl = "";

		String url = "http://" + difyInsInfo.getIp() + ":" + difyInsInfo.getPort() + "/console/api/apps/" + appId;
		JSONObject jsonObject = requestGet(difyInsInfo, url);

		// 检查JSON对象是否为空或不包含所需字段
		if (jsonObject == null || !jsonObject.containsKey("site") || !jsonObject.containsKey("mode")) {
			log.error("Invalid JSON response or missing fields.");
			return iframeUrl;
		}

		String code = jsonObject.getJSONObject("site").getString("code");
		String mode = jsonObject.getString("mode");

		// 根据不同的模式设置iframeUrl的路径
		switch (mode) {
			case "workflow":
				iframeUrl += "/workflow/";
				break;
			case "chat":
			case "agent-chat":
			case "advanced-chat":
				iframeUrl += "/chatbot/";
				break;
			case "completion":
				iframeUrl += "/completion/";
				break;
			default:
				log.warn("Unknown mode: {}", mode);
				break;
		}

		iframeUrl += code;

		return iframeUrl;
	}

	/**
	 * 获取会话列表
	 * 从指定门户的数据库中获取用户的会话列表
	 * 直接访问数据库查询会话信息，并按更新时间降序排序
	 * 返回的会话列表包含会话ID、应用ID、会话名称、创建时间和更新时间
	 */
	@Override
	public List<ConversationEntity> getConvserationList(String portalCode, String userId, int limit) {
		List<ConversationEntity> resultList = new ArrayList<>();
		String url = "jdbc:postgresql://" + dbIP + ":" + dbPort + "/dify_" + portalCode;

		String sql = "SELECT c.id AS conversation_id, c.app_id, c.\"name\" AS conversation_name, c.created_at, c.updated_at, a.\"name\" as app_name, a.icon, a.icon_background, a.icon_type " +
				"FROM conversations c " +
				"LEFT JOIN apps a ON c.app_id = a.id " +
				"LEFT JOIN end_users eu ON c.from_end_user_id = eu.id " +
				"WHERE eu.session_id = ? AND c.is_deleted = false ORDER BY c.updated_at DESC LIMIT ?;";

		try (Connection conn = DriverManager.getConnection(url, dbUser, dbPwd);
		     PreparedStatement pstmt = conn.prepareStatement(sql)) {

			pstmt.setString(1, userId);
			pstmt.setInt(2, limit);

			try (ResultSet rs = pstmt.executeQuery()) {
				boolean hasCalDify = false;
				while (rs.next()) {
					ConversationEntity entity = new ConversationEntity();
					entity.setConversationId(rs.getString("conversation_id"));
					entity.setAppId(rs.getString("app_id"));
					entity.setConversationName(rs.getString("conversation_name"));
					entity.setCreatedAt(rs.getTimestamp("created_at"));
					entity.setUpdatedAt(rs.getTimestamp("updated_at"));
					entity.setAppName(rs.getString("app_name"));
					entity.setIcon(rs.getString("icon"));
					entity.setIconBackground(rs.getString("icon_background"));
					entity.setIconType(rs.getString("icon_type"));
					String urlCache = appIconUrlCache.getIfPresent(portalCode + entity.getAppId());
					if (StringUtils.isBlank(urlCache) && !hasCalDify) {
						//调用dify，获取所有应用的iconUrl，存入缓存
						updateAppIconUrls(portalCode);
						hasCalDify = true;
						urlCache = appIconUrlCache.getIfPresent(portalCode + entity.getAppId());
					}
					entity.setSourceUrl(urlCache);
					entity.setUserId(userId);

					//调整+8时区
					entity.setCreatedAt(Date.from(entity.getCreatedAt().toInstant().plus(Duration.ofHours(8))));
					entity.setUpdatedAt(Date.from(entity.getUpdatedAt().toInstant().plus(Duration.ofHours(8))));

					resultList.add(entity);
				}
			}
			log.info("Successfully retrieved {} conversations for user {}", resultList.size(), userId);
		} catch (SQLException e) {
			log.error("Error retrieving conversation list: {}", e.getMessage(), e);
			throw new BusinessException(DifyErrorCode.QUERY_DB_GET_CONVERSATION_LIST_ERROR);
		}

		return resultList;
	}

	private void updateAppIconUrls(String portalCode) {
		// 根据portalCode获取Dify实例IP和端口
		DifyInstanceInfoVo difyInstanceInfo = iPortalService.getDifyInstanceInfoByPortalCode(portalCode);
		DifyInsInfo difyInsInfo = new DifyInsInfo(difyInstanceInfo.getInstanceIp(), difyInstanceInfo.getInstancePort());

		// 查询该Dify实例下所有的应用iconUrl，存入缓存
		int page = 1;
		String url = "http://" + difyInsInfo.getIp() + ":" + difyInsInfo.getPort() + "/console/api/apps?page=" + page + "&limit=100&name=&is_created_by_me=false";
		JSONObject jsonObject = requestGet(difyInsInfo, url);
		JSONArray jsonArray = jsonObject.getJSONArray("data");
		while (jsonObject.containsKey("has_more") && jsonObject.getString("has_more").equals("true")) {
			page++;
			url = "http://" + difyInsInfo.getIp() + ":" + difyInsInfo.getPort() + "/console/api/apps?page=" + page + "&limit=100&name=&is_created_by_me=false";
			jsonObject = requestGet(difyInsInfo, url);
			jsonArray.addAll(jsonObject.getJSONArray("data"));
		}

		//遍历结果
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject app = jsonArray.getJSONObject(i);
			if (app.getString("icon_type").equals("emoji")) {
				continue;
			}
			String appId = app.getString("id");
			String iconUrl = app.getString("icon_url");
			String key = portalCode + appId;
			appIconUrlCache.put(key, iconUrl);
		}

	}

	/**
	 * 删除会话
	 * 删除指定用户在指定应用中的会话
	 * 先进行权限校验，确认会话属于指定用户，然后执行删除操作
	 * 返回删除操作是否成功
	 */
	@Override
	public void removeConversation(String portalCode, String userId, String conversationId) {
		String url = "jdbc:postgresql://" + dbIP + ":" + dbPort + "/dify_" + portalCode;
		log.info("开始删除会话，门户代码: {}, 用户ID: {}, 会话ID: {}", portalCode, userId, conversationId);

		// 权限校验SQL：查询会话是否属于指定用户
		String checkPermissionSql = "SELECT COUNT(*) FROM conversations c " +
				"LEFT JOIN end_users eu ON c.from_end_user_id = eu.id " +
				"WHERE c.id = CAST(? AS UUID) AND eu.session_id = ? AND c.is_deleted = false";

		// 删除会话SQL
		String deleteSql = "DELETE FROM conversations WHERE id = CAST(? AS UUID)";

		try (Connection conn = DriverManager.getConnection(url, dbUser, dbPwd)) {
			// 1. 权限校验
			try (PreparedStatement checkStmt = conn.prepareStatement(checkPermissionSql)) {
				checkStmt.setString(1, conversationId);
				checkStmt.setString(2, userId);

				try (ResultSet rs = checkStmt.executeQuery()) {
					if (rs.next() && rs.getInt(1) == 0) {
						log.error("会话不存在或用户无权限删除该会话，用户ID: {}, 会话ID: {}", userId, conversationId);
						throw new BusinessException(DifyErrorCode.CONVERSATION_PERMISSION_DENIED);
					}
				}
			}

			// 2. 执行删除操作
			try (PreparedStatement deleteStmt = conn.prepareStatement(deleteSql)) {
				deleteStmt.setString(1, conversationId);
				int affectedRows = deleteStmt.executeUpdate();

				if (affectedRows > 0) {
					log.info("成功删除会话，会话ID: {}, 影响行数: {}", conversationId, affectedRows);
				} else {
					log.error("删除会话失败，未找到对应的会话记录，会话ID: {}", conversationId);
				}
			}

		} catch (SQLException e) {
			log.error("删除会话时发生数据库错误，会话ID: {}, 错误信息: {}", conversationId, e.getMessage(), e);
			throw new BusinessException(DifyErrorCode.QUERY_DB_DELETE_CONVERSATION_ERROR);
		}
	}

	/**
	 * 重命名会话
	 * 为指定用户在指定应用中的会话设置新名称
	 * 先进行权限校验，确认会话属于指定用户，然后执行重命名操作
	 * 返回重命名操作是否成功
	 */
	@Override
	public void renameConversation(String portalCode, String userId, String conversationId, String newName) {
		String url = "jdbc:postgresql://" + dbIP + ":" + dbPort + "/dify_" + portalCode;
		log.info("开始重命名会话，门户代码: {}, 用户ID: {}, 会话ID: {}, 新名称: {}", portalCode, userId, conversationId, newName);

		// 权限校验SQL：查询会话是否属于指定用户
		String checkPermissionSql = "SELECT COUNT(*) FROM conversations c " +
				"LEFT JOIN end_users eu ON c.from_end_user_id = eu.id " +
				"WHERE c.id = CAST(? AS UUID) AND eu.session_id = ? AND c.is_deleted = false";

		// 重命名会话SQL
		String updateSql = "UPDATE conversations SET name = ? WHERE id = CAST(? AS UUID)";

		try (Connection conn = DriverManager.getConnection(url, dbUser, dbPwd)) {
			// 1. 权限校验
			try (PreparedStatement checkStmt = conn.prepareStatement(checkPermissionSql)) {
				checkStmt.setString(1, conversationId);
				checkStmt.setString(2, userId);

				try (ResultSet rs = checkStmt.executeQuery()) {
					if (rs.next() && rs.getInt(1) == 0) {
						log.error("用户无权限重命名该会话，用户ID: {}, 会话ID: {}", userId, conversationId);
						throw new BusinessException(DifyErrorCode.CONVERSATION_PERMISSION_DENIED);
					}
				}
			}

			// 2. 执行重命名操作
			try (PreparedStatement updateStmt = conn.prepareStatement(updateSql)) {
				updateStmt.setString(1, newName);
				updateStmt.setString(2, conversationId);
				int affectedRows = updateStmt.executeUpdate();

				if (affectedRows > 0) {
					log.info("成功重命名会话，会话ID: {}, 新名称: {}, 影响行数: {}", conversationId, newName, affectedRows);
				} else {
					log.error("重命名会话失败，未找到对应的会话记录，会话ID: {}", conversationId);
				}
			}

		} catch (SQLException e) {
			log.error("重命名会话时发生数据库错误，会话ID: {}, 错误信息: {}", conversationId, e.getMessage(), e);
			throw new BusinessException(DifyErrorCode.QUERY_DB_RENAME_CONVERSATION_ERROR);
		}
	}

	/**
	 * 发送GET请求
	 * 向Dify实例发送GET请求，并处理响应
	 * 使用管理员token进行认证，返回响应的JSON对象
	 */
	private JSONObject requestGet(DifyInsInfo difyInsInfo, String url) throws BusinessException {
		String token = difyTokenUtil.getToken(difyInsInfo);
		log.info("Executing GET request to URL: {}", url);
		try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
			HttpGet request = new HttpGet(url);
			request.setHeader("Content-Type", "application/json");
			request.setHeader("Authorization", "Bearer " + token);

			try (CloseableHttpResponse response = httpClient.execute(request)) {
				int statusCode = response.getStatusLine().getStatusCode();
				log.info("GET request returned status code: {}", statusCode);
				HttpEntity entity = response.getEntity();
				if (entity != null) {
					String responseString = EntityUtils.toString(entity);
					log.info("Response content: {}", responseString);
					return JSONObject.parseObject(responseString);
				}
			}
		} catch (IOException e) {
			log.error("Error occurred while executing GET request: {}", e.getMessage(), e);
		}
		return null;
	}

	/**
	 * 发送POST请求
	 *
	 * @param difyInsInfo Dify实例信息对象
	 * @param url         请求URL
	 * @param body        请求体内容（JSON格式字符串）
	 * @return 响应的JSON对象
	 */
	private JSONObject requestPost(DifyInsInfo difyInsInfo, String url, String body) {
		String token = difyTokenUtil.getToken(difyInsInfo);
		log.info("Executing POST request to URL: {}", url);
		log.info("Request body: {}", body);
		try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
			HttpPost request = new HttpPost(url);
			request.setHeader("Content-Type", "application/json");
			request.setHeader("Authorization", "Bearer " + token);

			// 设置请求体
			if (StringUtils.isNotBlank(body)) {
				StringEntity entity = new StringEntity(body, "UTF-8");
				request.setEntity(entity);
			}

			try (CloseableHttpResponse response = httpClient.execute(request)) {
				int statusCode = response.getStatusLine().getStatusCode();
				log.info("POST request returned status code: {}", statusCode);
				HttpEntity entity = response.getEntity();
				if (entity != null) {
					String responseString = EntityUtils.toString(entity);
					log.info("Response content: {}", responseString);
					return JSONObject.parseObject(responseString);
				}
			}
		} catch (IOException e) {
			log.error("Error occurred while executing POST request: {}", e.getMessage(), e);
		}
		return null;
	}

	/**
	 * 获取指定应用的API密钥
	 * 尝试获取应用的API密钥，如果不存在则自动创建一个新的
	 * 这个API密钥用于调用应用的各种功能，如删除会话、重命名会话等
	 *
	 * @param difyInsInfo Dify实例信息对象
	 * @param appId       应用ID
	 * @return 应用的API密钥，如果获取或创建失败则返回null
	 */
	private String retrieveAppKey(DifyInsInfo difyInsInfo, String appId) {
		String apiKeyUrl = "http://" + difyInsInfo.getIp() + ":" + difyInsInfo.getPort() + "/console/api/apps/" + appId + "/api-keys";
		log.info("Retrieving API key for app ID: {}", appId);
		JSONObject apiKeyResponse = requestGet(difyInsInfo, apiKeyUrl);
		if (apiKeyResponse != null && apiKeyResponse.containsKey("data")) {
			JSONArray apiKeys = apiKeyResponse.getJSONArray("data");
			if (apiKeys.size() > 0) {
				log.info("API key retrieved successfully for app ID: {}", appId);
				return apiKeys.getJSONObject(0).getString("token");
			} else {
				log.info("No existing API keys found for app ID: {}. Creating a new one.", appId);
				// 创建新的应用key
				JSONObject createKeyResponse = requestPost(difyInsInfo, apiKeyUrl, "{}");
				if (createKeyResponse != null && createKeyResponse.containsKey("token")) {
					log.info("New API key created successfully for app ID: {}", appId);
					return createKeyResponse.getString("token");
				} else {
					log.error("Failed to create a new API key for app ID: {}", appId);
				}
			}
		} else {
			log.error("Failed to retrieve API keys for app ID: {}", appId);
		}
		return null;
	}
}
