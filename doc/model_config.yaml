# 预置模型配置
models:
  - name: "Qwen3-30B-A3B"    # 模型名称
    displayName: "Qwen3-30B-A3B"   #模型显示名称
    modelType: "llm"   # 模型类型   llm  rerank  text-embedding  speech2text  tts
    apiKey: "sm-ME_IItdVGpeg7ekgm9lfimC9kukp1S37J4E3KL2AxAo="
    endpointUrl: "http://secullm:8000/v1"
    completionMode: "chat"  # chat  completion
    contextSize: 32768  # 模型上下文长度
    maxTokensToSample: 4096 # 最大token上限
    visionSupport: "no_support"   #是否支持视觉功能   no_support   support
  - name: "ritrieve_zh_v1"    # 模型名称
    displayName: "ritrieve_zh_v1"   #模型显示名称
    modelType: "text-embedding"   # 模型类型   llm  rerank  text-embedding  speech2text  tts
    apiKey: "sm-ME_IItdVGpeg7ekgm9lfimC9kukp1S37J4E3KL2AxAo="
    endpointUrl: "http://secullm:8000/v1"
    completionMode: "chat"  # chat  completion
    contextSize: 32768  # 模型上下文长度
    maxTokensToSample: 4096 # 最大token上限
    visionSupport: "no_support"   #是否支持视觉功能   no_support   support

# 系统模型设置
systemModels:
  llm: Qwen3-30B-A3B
  text-embedding: embedding
  rerank:
  speech2text:
  tts:
