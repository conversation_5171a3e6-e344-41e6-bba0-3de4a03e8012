{"type": "object", "properties": {}, "x-apifox-orders": ["01JTPXRB6YQ1PQKCBR7X1BJFS0"], "x-apifox-refs": {"01JTPXRB6YQ1PQKCBR7X1BJFS0": {"$ref": "#/definitions/165493909", "x-apifox-overrides": {"result": {"type": ["object", "null"], "properties": {"id": {"type": "long", "description": "门户ID"}, "portalName": {"type": "string", "description": "门户名称"}, "portalCode": {"type": "string", "description": "门户编码/URL前缀/实例前缀"}, "portalTitle": {"type": "string", "description": "门户标题"}, "portalLogo": {"type": "string", "description": "门户logo，base64"}, "defaultAppId": {"type": "string", "description": "默认应用ID"}, "defaultAppName": {"type": "string", "description": "默认应用名称"}, "status": {"type": "integer", "description": "门户状态{@link com.sansec.ai.portal.constant.PortalStatusEnum}"}}, "description": "返回数据"}}, "required": ["result"]}}}