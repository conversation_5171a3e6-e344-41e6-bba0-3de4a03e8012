@startuml
title 更新管理员账号密码

participant "统一门户\n后端服务" as service
participant "Dify api" as difyapi
participant "Dify实例" as dify

autonumber "<b>1-0</b>"

group 更新管理员账号密码
  service -> difyapi:执行门户管理员账号密码更新
    note left
      dify实例真实端口
      门户管理员账号、旧密码、新密码
    end note
  group 单例模式获取dify实例token
    difyapi -> difyapi: 查询内存中是否包含该门户的token
    difyapi -> difyapi: token是否快过期
      group 更新token
        difyapi -> dify: 登录管理员账户获取token
        dify -> difyapi: 新的token
        difyapi -> difyapi: 更新内存中的token
      end
  end

  difyapi -> dify: 调用密码变更接口
  dify --> difyapi: 执行结果
  difyapi -> service: 返回结果及失败原因（如果失败）
@enduml