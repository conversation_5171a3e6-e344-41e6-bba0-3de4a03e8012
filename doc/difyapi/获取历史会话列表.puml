@startuml
title 获取历史会话列表

legend top
  涉及改造Dify接口
  改造前：解析api密钥中的app_id，根据user_id返回app_id应用下的历史会话
  改造后：
  ①支持根据user_id返回该用户所有应用下的历史会话
  ②绕过应用api密钥校验
  ③返回里增加app_id
end legend

participant "前端" as web
participant "统一门户\n后端服务" as service
participant "Dify api" as difyapi
participant "Dify实例" as dify

autonumber "<b>1-0</b>"

group 获取历史会话列表
  web -> service:获取历史会话列表
    note left
      user_id：当前登录用户id，必须
      limit: （选填）返回多少条记录， 默认20条
      sort_by：（选填）排序字段，默认 -updated_at(按更新时间倒序排列)
            可选值：created_at, -created_at, updated_at, -updated_at
    end note
  service -> service: 根据用户获取所属门户
  service -> difyapi: 调用获取历史会话接口
    note left
      dify实例真实端口
      user_id
      limit
    end note
  difyapi -> dify: 调用改造后接口
  dify -> difyapi: 执行结果
  difyapi -> service: 历史会话列表
  service -> web: 返回历史会话列表
    note left
      data (array[object]) 会话列表
        id (string) 会话 ID
        name (string) 会话名称，默认为会话中用户最开始问题的截取。
        inputs (object) 用户输入参数。
        status (string) 会话状态
        introduction (string) 开场白
        created_at (timestamp) 创建时间
        updated_at (timestamp) 更新时间
        app_id 应用id
    end note
@enduml