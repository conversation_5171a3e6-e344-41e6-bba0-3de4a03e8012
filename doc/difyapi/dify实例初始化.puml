@startuml
title Dify初始化

participant "统一门户\n后端服务" as service
participant "Dify api" as difyapi
participant "Dify实例" as dify
participant "模型配置文件" as ini
participant "数据库" as db

autonumber "<b>1-0</b>"

group 初始化Dify
  service -> difyapi:执行Dify初始化操作
    note left
      dify实例真实端口
      门户管理员账号密码
    end note
  group 系统初始化
    difyapi -> dify: 设置管理员
    dify --> difyapi: 执行结果（成功/失败）
    difyapi -> dify: 登录管理员
    dify --> difyapi: 登录token
    difyapi -> difyapi: 保存token
  end
  group 配置自有模型
    difyapi -> ini: 查询模型框架配置
    ini --> difyapi: 模型框架配置
      note right
        模型访问URL（SecuLlama或VLLM管理服务）
        模型名称、模型显示名称
        模型类型（LLM、Text Embedding、Rerank）
        模型参数：
          Completion mode（对话、补全）
          视觉多模态（支持、不支持）
          上下文长度
          最大token
          ...
      end note

    difyapi -> db: 查询模型apikey
    db --> difyapi: 模型apikey
      note right
        多个模型用一个apikey
        apikey存放在数据库里
      end note

    loop 根据模型配置列表遍历
      difyapi -> dify: 模型供应商添加模型
      dify --> difyapi: 执行结果
    end loop
  end
  group 配置系统模型
    difyapi -> ini: 查询系统模型配置
    ini --> difyapi: 系统模型配置
    note right
      系统推理模型：模型名称、供应商（可空）
      Embedding模型：模型名称、供应商（可空）
      Rerank模型：模型名称、供应商（可空）
      语音转文本模型：模型名称、供应商（可空）
      文本转语音模型：模型名称、供应商（可空）
    end note

    difyapi -> dify: 配置系统模型
    dify --> difyapi: 执行结果
  end
  difyapi -> difyapi: 判断执行结果，组装失败原因（如果失败）
  difyapi -> service: 返回初始化结果及失败原因（如果失败）
@enduml