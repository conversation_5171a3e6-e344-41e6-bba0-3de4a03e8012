@startuml
title 获取内嵌chat页面URL

participant "前端" as web
participant "统一门户\n后端服务" as service
participant "Dify api" as difyapi
participant "Dify实例" as dify

autonumber "<b>1-0</b>"

group 获取内嵌chat页面URL
  web -> service:查询内嵌chat页面URL
    note left
      user_id：当前登录用户id，必须
      app_id:  应用id，空则代表默认应用
    end note
  service -> service: 根据用户获取所属门户
  service -> difyapi: 调用获取URL接口
    note left
      dify实例真实端口
      appId
    end note
  group 单例模式获取dify实例token
  difyapi -> difyapi: 查询内存中是否包含该门户的token
  difyapi -> difyapi: token是否快过期
    group 更新token
      difyapi -> dify: 登录管理员账户获取token
      dify -> difyapi: 新的token
      difyapi -> difyapi: 更新内存中的token
    end
  end
  difyapi -> dify: 获取应用的公开访问URL
    note right
      接口：/console/api/apps/{app_id}
    end note
  dify -> difyapi: 执行结果
  difyapi -> service: 公开访问URL
  service -> service: 拼接参数
    note left
      对user_id进行compressAndEncodeBase64操作
      拼接sys.user_id到URL中
    end note
  service -> web: 返回结果
@enduml