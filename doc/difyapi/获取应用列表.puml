@startuml
title 获取应用列表

participant "前端" as web
participant "统一门户\n后端服务" as service
participant "Dify api" as difyapi
participant "Dify实例" as dify

autonumber "<b>1-0</b>"

group 获取应用列表
  web -> service:获取应用列表
    note left
      user_id：当前登录用户id，必须
      应用类型：聊天应用、智能体......   空则代表全部
    end note
  service -> service: 根据用户查询所属门户
  service -> difyapi: 获取门户下所有应用
    note left
      dify实例真实端口
      应用类型
    end note

    group 单例模式获取dify实例token
    difyapi -> difyapi: 查询内存中是否包含该门户的token
    difyapi -> difyapi: token是否快过期
      group 更新token
        difyapi -> dify: 登录管理员账户获取token
        dify -> difyapi: 新的token
        difyapi -> difyapi: 更新内存中的token
      end
    end
  difyapi -> dify: 查询
    note right
      接口：/console/api/apps
    end note
  dify -> difyapi: 查询结果
  difyapi -> service: 应用列表
  service --> service: 数据处理
  service -> web: 应用列表

@enduml