@startuml
title 创建门户流程
legend top
  权限归属：系统管理员
end legend

actor "系统管理员" as user
participant "统一门户\n后端服务" as service
database "数据库" as db
participant "docker api" as docker

autonumber

user -> service: 创建门户
note left
  门户名称
  门户标题
  logo
  访问地址前缀（实例前缀）
  选择主题
  配置默认应用
  门户管理员
    管理员账号
    管理员密码
end note

service -> docker: 检查实例名称是否可用

docker --> service:

service -> db: 保存门户信息

service -> service: 创建实例信息

service -> db: 保存门户和实例的关联关系
note left
  实例信息：
    数据挂载目录：
      app/storage：
        知识库
        上传的图片
        生成的文件
        租户的私钥
      向量数据库
      redis
      sandbox
      插件
    API前缀
    容器名称前缀
end note

service -> service: 执行异步初始化任务

service --> user: 创建门户成功，状态：初始化中

group 异步初始化任务
  note over user, docker
    该部分流程参见《Dify实例部署流程》
  end note
end group

loop 10s
  user -> service: 获取门户状态
  service --> user:
end loop


@enduml
