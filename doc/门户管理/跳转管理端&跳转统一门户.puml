@startuml
title 跳转管理端&跳转统一门户
legend top
  管理端和统一门户的URL由后台维护，后端提供URL查询接口
  当用户点击跳转按钮时，前端先根据门户ID查询对应的URL，然后打开URL即可
end legend

participant "Dify管理端" as dify
participant "统一门户" as hub
actor "管理员" as user
participant "统一门户\n后端服务" as service
database "数据库" as db

autonumber

user -> user: 跳转管理端/跳转统一门户

user -> service: 查询URL
note left
  门户ID
  类型：管理端、统一门户
end note

service -> db: 查询门户信息

service --> service: 根据前缀拼接URL

service --> user: URL

alt 管理端
  user -> dify: 打开URL
else 统一门户
  user -> dify: 打开URL
end

@enduml