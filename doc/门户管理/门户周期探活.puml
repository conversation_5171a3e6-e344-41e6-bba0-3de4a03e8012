@startuml
title 门户周期探活
legend top
  后台通过定时任务，检查各门户的运行状态是否正常，业务逻辑如下：
  1. 根据状态机，只检查处于特定状态的门户：运行中、异常
  2. 定时任务每隔10s执行一次，每次遍历所有实例，分别检查
  3. 一旦检测到实例状态发生变化，立即更新到数据库
end legend

participant "定时任务" as task
participant "统一门户\n后端服务" as service
database "数据库" as db
participant "docker api" as docker

autonumber

task -> service: 触发门户周期探活任务
note left
  0/10 * * * * ?
end note

service -> db: 根据门户状态，查询门户列表
note left
  门户状态：
    运行中
    异常
end note

db --> service: 门户列表
note right
  门户列表
end note

loop 门户列表
  service -> db: 查询门户对应的Dify实例信息

  service -> docker: Dify实例的各容器是否存在
  docker --> service:

  service -> docker: 检查各容器状态
  docker --> service:

  alt 全部容器状态正常
    service -> db: 更新门户状态：运行中
  else 有容器不存在
    service -> db: 更新门户状态：异常
  else 有容器状态异常
    service -> db: 更新门户状态：异常
  end
end loop

service --> task: 执行结束

@enduml
