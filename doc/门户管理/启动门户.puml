@startuml
title 启动门户
legend top
  权限归属：系统管理员
  1. 启动门户时，后端自动拉起门户对应的Dify实例
  2. 后端根据Dify实例状态，更新门户状态
  3. 后端周期探测Dify实例状态，并同步更新门户状态
end legend

actor "系统管理员" as user
participant "统一门户\n后端服务" as service
database "数据库" as db
participant "docker api" as docker

autonumber

user -> service: 启动门户
note left
  门户ID
end note

service -> db: 根据状态机，判断门户状态是否支持启动

service -> db: 更新门户状态：启动中

service -> service: 执行异步启动任务

group 异步启动任务
  service -> db: 查询门户对应的Dify实例信息

  service -> docker: Dify实例的各容器是否存在

  alt 某容器不存在
    service -> db: 更新门户状态：异常
  else
    service -> docker: docker compose启动Dify实例
    docker --> service:

    loop 10s
      service -> docker: 检查各容器状态
      docker --> service:

      alt 全部容器状态正常
        service -> db: 更新门户状态：运行中
      else 有容器存在，超过N次
        service -> db: 更新门户状态：异常
      end
    end loop
  end


end group

loop 10s
  user -> service: 获取门户状态
  service --> user:
end loop


@enduml
