@startuml
title 停止门户
legend top
  权限归属：系统管理员
  1. 停止门户时，后端也一并停止门户对应的Dify实例
end legend

actor "系统管理员" as user
participant "统一门户\n后端服务" as service
database "数据库" as db
participant "docker api" as docker

autonumber

user -> service: 停止门户
note left
  门户ID
end note

service -> docker: docker compose停止Dify实例

opt 停止失败
  docker --> service: 停止失败
  service --> user: 停止失败，原因XXX
end

service -> db: 更新门户状态：已停止

service --> user: 停止成功


@enduml
