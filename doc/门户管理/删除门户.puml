@startuml
title 删除门户
legend top
  权限归属：系统管理员
  1. 删除门户时需要先确认门户状态是否支持删除
  2. 删除门户前，需要让用户二次确认：门户数据删除、门户管理员删除、门户用户保留
  3. 需要删除对应的Dify实例下的容器，以及挂载目录的数据
  4. 删除实例信息、门户信息、门户和实例的绑定关系
  5. 删除门户管理员，保留用户信息，解除用户和实例的绑定关系
end legend

actor "系统管理员" as user
participant "统一门户\n后端服务" as service
database "数据库" as db
participant "docker api" as docker

autonumber

user -> user: 删除门户

user -> user: 二次确认
note left
  此操作会门户在平台中的所有数据，包括：
    门户所有配置数据
    门户下所有应用和数据
    门户下所有知识库和文档数据
    门户管理员
    用户数据：收藏记录、历史会话记录、上传的文件
    门户用户保留，系统管理员可在用户列表查看
  确认删除？
end note

user -> service: 删除门户
note left
  门户ID
end note

service -> db: 判断门户状态是否支持删除
note left
  状态为创建中、运行中、删除中的门户不支持删除
end note

service -> db: 更新门户状态：删除中

service -> service: 执行异步删除任务

service --> user: 正在删除门户

group 异步删除任务
  service -> db: 查询门户对应的Dify实例信息

  service -> docker: docker compose删除Dify实例
  docker --> service:

  loop 10s
    service -> docker: 检查各容器是否存在
    docker --> service:

    alt 全部容器都不存在
      service -> db: 更新门户状态：删除成功
    else 有容器存在，超过N次
      service -> db: 更新门户状态：删除失败
    end
  end loop

end group

loop 10s
  user -> service: 获取门户状态
  service --> user:
end loop


@enduml
