@startuml
title Dify实例部署流程
legend top
  基于Docker Compose实现Dify容器化部署，实现在一台宿主机上部署多个实例，核心点如下：
  准备阶段：
    1.多个实例共用业务数据库服务，但通过模式隔离（或数据库隔离），首先要初始化数据库
    2.初始化配置文件，包括：
        1.每个实例使用独立的网络，实现网络隔离
        2.部署时需要配置对外暴露的端口，不能重复
        3.知识库目录需要透明加密，部署时需要挂载目录：透明加密根路径+Dify实例知识库独立路径
        4.其它目录如向量数据库、redis、sandbox、插件，需要统一根据路径，各实例独立配置
        5.其它配置项：
          - 配置数据库连接信息
          - 对外API地址
          - 文件下载地址
          - 服务API地址
          - SECRET_KEY
  实例部署阶段：
    1.创建Dify实例所需的各容器（创建方式待定）
    2.创建过程持续时间较长，需要定时检查创建结果
  实例初始化阶段：
    1.通过API完成系统初始化，写入门户管理员账号、密码
    2.模型信息，通过一体机管理系统API拉取
    3.Dify部署好后，自动完成供应商、模型的初始化
    4.更新预置应用绑定的模型信息（或更新默认模型的配置）

end legend
actor "异步任务" as task
participant "统一门户\n后端服务" as service
participant "Dify部署包\n脚本+配置" as deploy
database "数据库服务" as db
participant "Docker API\nSDK" as docker
participant "Dify API\nSDK" as difyapi
participant "Dify实例\nDocker容器集合" as dify


task -> service: 异步任务开始
'准备阶段==================================================
note over task, dify
    1.准备阶段：初始化数据库、配置文件
end note
autonumber "<b>1-0</b>"
service -> service: 实例信息
note left
  实例前缀
  实例端口
  网关地址
  门户管理员账号、密码
  数据库连接地址
  数据库账号、密码
end note
service -> deploy: 创建数据库
note left
参数：
  实例前缀
  数据库连接地址
  数据库账号、密码
end note
deploy -> db: <color red><b>执行数据库初始化脚本
note left
输入：
    上游参数
    脚本文件
    初始化SQL
输出：
    DB: dify_{实例前缀}
    DB: dify_plugin_{实例前缀}
end note
deploy --> service: 执行结果：成功/失败
opt error
service --> db: 门户初始化失败
note left
    更新门户状态：初始化失败
    原因：数据库初始化失败
end note
service --> task

end
service -> deploy: 生成配置文件
note left
  网关地址
  实例前缀
  数据库连接地址
  数据库账号、密码
  SECRET_KEY
  知识库挂载目录
  数据挂载目录
end note
deploy -> deploy: <color red><b>执行生成配置文件脚本
note left
输入：
    上游参数
    脚本文件
输出：
    配置文件: .env-{实例前缀}
end note

'部署阶段==================================================
note over task, dify
    2.实例部署阶段：部署Dify实例
end note
autonumber "<b>2-0</b>"

service -> docker: 检查实例前缀是否可用\n根据容器名称模糊查询容器列表

docker --> service

opt error
    service --> db: 门户初始化失败
    note left
        更新门户状态：初始化失败
        原因：容器创建失败，实例名称重复
    end note
    service --> task
end

service -> docker: docker compose创建Dify实例的各容器
note left
    实例前缀
end note

docker -> dify
dify --> docker

loop 循环检查部署结果：10s, N次
    service -> docker: 根据实例前缀，查询容器列表
    note left
        容器列表
    end note

    opt error 容器列表不全
        service --> service: 继续循环
    end

    loop 遍历容器列表
        service -> docker: 查询容器状态
        opt error 容器状态 != health
            service --> service: 继续循环
        end
    end loop

    opt error 循环超过N次
        service --> db: 门户初始化失败
        note left
            更新门户状态：初始化失败
            原因：实例已创建，实例状态检查失败
        end note
        service --> task
    end

    service --> service: 结束循环，到下一阶段
end loop

'初始化阶段==================================================
note over task, dify
    3.实例初始化阶段：预置数据
end note
autonumber "<b>3-0</b>"

service -> service: 所需上游参数
note left
    实例端口
    实例IP
    实例前缀
    门户管理员账号、密码
    数据库连接地址
    数据库账号、密码
end note

service -> difyapi: 系统初始化
difyapi -> dify: 系统初始化：/console/api/setup \n/console/api/init
difyapi -> dify: 设置管理员账户：/console/api/setup
note left
    账号：门户管理员账号
    邮箱：门户管理员账号@sansec.com
    密码：门户管理员密码
end note

difyapi -> dify: 初始化供应商\n添加模型\n默认系统模型设置


difyapi --> service: 初始化结果：成功/失败

alt 初始化结果=失败
    service --> db: 门户初始化失败
    note left
        更新门户状态：初始化失败
        原因：执行系统初始化失败
    end note
    service --> task
else 初始化结果=失败
    service --> db: 门户初始化成功
    note left
        更新门户状态：运行中
    end note
    service --> task
end

@enduml